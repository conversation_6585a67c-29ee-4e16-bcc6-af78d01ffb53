#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动重构file_operations.py中的同步包装器方法

将复杂的事件循环检测逻辑替换为统一的_run_async_method调用

作者: AI助手
日期: 2025-01-30
"""

import re
import os

def refactor_sync_wrapper_methods():
    """重构同步包装器方法"""
    
    file_path = "src/core/file_operations.py"
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 定义需要替换的模式
    old_pattern = r'''            # 检查是否在事件循环中运行
            try:
                loop = asyncio\.get_running_loop\(\)
                # 如果在事件循环中，创建任务而不是使用 asyncio\.run\(\)
                task = loop\.create_task\(([^)]+)\)
                result = asyncio\.run_coroutine_threadsafe\(task, loop\)\.result\(\)
            except RuntimeError:
                # 没有运行中的事件循环，使用 asyncio\.run\(\)
                result = asyncio\.run\(([^)]+)\)'''
    
    # 新的替换内容
    new_pattern = r'''            # 使用统一的同步包装器
            result = self._run_async_method(\1)'''
    
    # 执行替换
    new_content = re.sub(old_pattern, new_pattern, content, flags=re.MULTILINE)
    
    # 处理直接使用asyncio.run()的情况
    direct_asyncio_pattern = r'return asyncio\.run\(([^)]+)\)'
    direct_asyncio_replacement = r'return self._run_async_method(\1)'
    
    new_content = re.sub(direct_asyncio_pattern, direct_asyncio_replacement, new_content)
    
    # 处理results = asyncio.run()的情况
    results_asyncio_pattern = r'results = asyncio\.run\(([^)]+)\)'
    results_asyncio_replacement = r'results = self._run_async_method(\1)'
    
    new_content = re.sub(results_asyncio_pattern, results_asyncio_replacement, new_content)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("文件重构完成！")
    
    # 统计替换次数
    old_count = content.count('asyncio.run(')
    new_count = new_content.count('asyncio.run(')
    replaced_count = old_count - new_count
    
    print(f"替换了 {replaced_count} 个 asyncio.run() 调用")
    print(f"剩余 {new_count} 个 asyncio.run() 调用")

if __name__ == "__main__":
    refactor_sync_wrapper_methods()
