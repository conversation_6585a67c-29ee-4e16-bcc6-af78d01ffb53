#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高性能异步任务管理器模块 - 重构版本

该模块提供统一的异步任务管理功能，支持：
1. 协程任务管理
2. 线程池任务管理
3. 进程池任务管理
4. 统一的任务状态和结果类型
5. 完善的错误处理和中断机制

重构特性：
- 智能任务调度器
- 动态资源池管理
- 任务优先级支持
- 性能监控和指标收集
- 负载均衡机制

更新日期: 2025-07-26
版本: 3.0.0 - 高性能重构版
"""

import asyncio
import concurrent.futures
import threading
import time
import heapq
import psutil
from typing import Dict, List, Callable, Any, Optional, Union, Tuple, Set
import uuid
import os
from dataclasses import dataclass, field
from enum import Enum
import weakref

from src.utils.logger import get_logger
from .event_system import get_event_system, EventPriority
from src.utils.format_utils import _normalize_path
from src.utils.unified_types import (
    TaskStatus, TaskType, UnifiedTaskResult, ProgressInfo,
    ProgressCallback, TaskCompleteCallback, create_task_result
)

logger = get_logger(__name__)

# 获取事件系统
event_system = get_event_system()

# 兼容性：保留旧的TaskResult类型别名
TaskResult = UnifiedTaskResult


class TaskPriority(Enum):
    """任务优先级枚举"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class PriorityTask:
    """优先级任务包装器"""
    priority: TaskPriority
    timestamp: float
    task_id: str
    func: Callable
    args: tuple
    kwargs: dict
    is_coroutine: bool = False

    def __lt__(self, other):
        # 优先级高的任务排在前面，时间戳早的排在前面
        if self.priority.value != other.priority.value:
            return self.priority.value > other.priority.value
        return self.timestamp < other.timestamp


@dataclass
class ResourcePoolMetrics:
    """资源池性能指标"""
    pool_type: str
    max_workers: int
    active_workers: int = 0
    pending_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0
    total_execution_time: float = 0.0
    average_execution_time: float = 0.0
    cpu_usage: float = 0.0
    memory_usage: float = 0.0

    def update_execution_time(self, execution_time: float):
        """更新执行时间统计"""
        self.total_execution_time += execution_time
        total_tasks = self.completed_tasks + self.failed_tasks
        if total_tasks > 0:
            self.average_execution_time = self.total_execution_time / total_tasks


@dataclass
class AsyncManagerMetrics:
    """异步管理器性能指标"""
    total_tasks_submitted: int = 0
    total_tasks_completed: int = 0
    total_tasks_failed: int = 0
    total_tasks_cancelled: int = 0
    current_active_tasks: int = 0
    max_concurrent_tasks: int = 0
    event_loop_utilization: float = 0.0
    system_cpu_usage: float = 0.0
    system_memory_usage: float = 0.0
    io_pool_metrics: ResourcePoolMetrics = field(default_factory=lambda: ResourcePoolMetrics("IO", 1))
    cpu_pool_metrics: ResourcePoolMetrics = field(default_factory=lambda: ResourcePoolMetrics("CPU", 1))
    process_pool_metrics: ResourcePoolMetrics = field(default_factory=lambda: ResourcePoolMetrics("Process", 1))

    def get_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        return {
            'total_submitted': self.total_tasks_submitted,
            'total_completed': self.total_tasks_completed,
            'total_failed': self.total_tasks_failed,
            'total_cancelled': self.total_tasks_cancelled,
            'success_rate': (
                self.total_tasks_completed / max(1, self.total_tasks_submitted) * 100
            ),
            'current_active': self.current_active_tasks,
            'max_concurrent': self.max_concurrent_tasks,
            'event_loop_utilization': self.event_loop_utilization,
            'system_cpu_usage': self.system_cpu_usage,
            'system_memory_usage': self.system_memory_usage,
            'pools': {
                'io': {
                    'max_workers': self.io_pool_metrics.max_workers,
                    'active_workers': self.io_pool_metrics.active_workers,
                    'pending_tasks': self.io_pool_metrics.pending_tasks,
                    'avg_execution_time': self.io_pool_metrics.average_execution_time
                },
                'cpu': {
                    'max_workers': self.cpu_pool_metrics.max_workers,
                    'active_workers': self.cpu_pool_metrics.active_workers,
                    'pending_tasks': self.cpu_pool_metrics.pending_tasks,
                    'avg_execution_time': self.cpu_pool_metrics.average_execution_time
                },
                'process': {
                    'max_workers': self.process_pool_metrics.max_workers,
                    'active_workers': self.process_pool_metrics.active_workers,
                    'pending_tasks': self.process_pool_metrics.pending_tasks,
                    'avg_execution_time': self.process_pool_metrics.average_execution_time
                }
            }
        }


class OptimizedAsyncManager:
    """
    高性能异步管理器类

    特性：
    - 智能任务调度器
    - 动态资源池管理
    - 任务优先级支持
    - 性能监控和指标收集
    - 负载均衡机制
    - 纯协程任务管理（合并自AsyncTaskManager）
    """
    _instance = None
    _lock = threading.Lock()

    def __new__(cls, *args, **kwargs):
        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self, enable_monitoring: bool = True, auto_scale: bool = True):
        if hasattr(self, '_initialized') and self._initialized:
            return

        self._cpu_count = os.cpu_count() or 1
        self._enable_monitoring = enable_monitoring
        self._auto_scale = auto_scale

        # 性能指标
        self._metrics = AsyncManagerMetrics()
        self._metrics.io_pool_metrics.max_workers = self._cpu_count * 4
        self._metrics.cpu_pool_metrics.max_workers = self._cpu_count * 2
        self._metrics.process_pool_metrics.max_workers = self._cpu_count

        # 初始化事件循环
        self._loop = None
        self._loop_thread = None
        self._running = False
        self._start_event_loop()

        # 智能资源池管理
        self._io_thread_pool = concurrent.futures.ThreadPoolExecutor(
            max_workers=self._cpu_count * 4,
            thread_name_prefix="OptimizedIO_Worker"
        )
        self._cpu_thread_pool = concurrent.futures.ThreadPoolExecutor(
            max_workers=self._cpu_count * 2,
            thread_name_prefix="OptimizedCPU_Worker"
        )
        self._process_pool = concurrent.futures.ProcessPoolExecutor(
            max_workers=self._cpu_count
        )

        # 任务管理
        self._tasks: Dict[str, UnifiedTaskResult] = {}
        self._tasks_lock = asyncio.Lock()  # 异步锁

        # 纯协程任务管理（合并自AsyncTaskManager）
        self._coroutine_tasks: Dict[str, UnifiedTaskResult] = {}
        self._interrupt_events: Dict[str, asyncio.Event] = {}

        # 优先级任务队列
        self._priority_queue: List[PriorityTask] = []
        self._queue_lock = asyncio.Lock()  # 优先级队列锁

        # 批量任务管理
        self._batch_tasks: Dict[str, List[str]] = {}
        self._batch_lock = asyncio.Lock()  # 批量任务锁

        # 性能监控
        self._monitoring_task = None
        self._last_metrics_update = time.time()

        # 负载均衡
        self._load_balancer_enabled = True
        self._pool_load_history = {
            'io': [],
            'cpu': [],
            'process': []
        }

        self._initialized = True
        logger.info(f"高性能异步管理器初始化完成 - CPU核心数: {self._cpu_count}, "
                   f"监控: {enable_monitoring}, 自动扩缩: {auto_scale}")

    def _start_event_loop(self):
        """启动专用的事件循环线程"""
        def run_loop():
            try:
                # 创建新的事件循环
                self._loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self._loop)

                # 初始化异步锁和监控
                async def init_async_components():
                    self._tasks_lock = asyncio.Lock()
                    self._batch_lock = asyncio.Lock()
                    self._queue_lock = asyncio.Lock()

                    # 启动性能监控
                    if self._enable_monitoring:
                        self._monitoring_task = asyncio.create_task(self._performance_monitor())

                    logger.info("高性能异步管理器事件循环已启动")

                # 在事件循环中初始化异步组件
                init_task = self._loop.create_task(init_async_components())
                self._loop.run_until_complete(init_task)
                self._running = True

                # 保持事件循环运行
                self._loop.run_forever()

            except Exception as e:
                logger.error(f"事件循环启动失败: {e}")
            finally:
                self._running = False
                logger.info("高性能异步管理器事件循环已停止")

        # 在单独的线程中启动事件循环
        self._loop_thread = threading.Thread(target=run_loop, daemon=True,
                                            name="OptimizedAsyncManager_EventLoop")
        self._loop_thread.start()

        # 等待事件循环启动
        timeout = 5.0
        start_time = time.time()
        while not self._running and (time.time() - start_time) < timeout:
            time.sleep(0.01)

        if not self._running:
            logger.error("事件循环启动超时")
        else:
            logger.info("事件循环启动成功")

        # 等待异步锁初始化完成
        timeout = 5.0
        start_time = time.time()
        while (self._tasks_lock is None or self._batch_lock is None or self._queue_lock is None) and (time.time() - start_time) < timeout:
            time.sleep(0.1)

        if self._tasks_lock is None:
            logger.error("异步锁初始化超时，尝试手动初始化")
            # 手动在事件循环中初始化锁
            if self._loop and self._loop.is_running():
                future = asyncio.run_coroutine_threadsafe(
                    self._manual_init_locks(), self._loop
                )
                try:
                    future.result(timeout=2.0)
                    logger.info("手动异步锁初始化完成")
                except Exception as e:
                    logger.error(f"手动初始化锁失败: {e}")
        else:
            logger.info("异步锁初始化完成")

    async def _manual_init_locks(self):
        """手动初始化异步锁"""
        self._tasks_lock = asyncio.Lock()
        self._batch_lock = asyncio.Lock()
        self._queue_lock = asyncio.Lock()

    async def _performance_monitor(self):
        """性能监控协程"""
        logger.info("性能监控已启动")

        while self._running:
            try:
                await asyncio.sleep(5.0)  # 每5秒更新一次指标
                await self._update_performance_metrics()

                # 自动扩缩容检查
                if self._auto_scale:
                    await self._check_auto_scaling()

            except asyncio.CancelledError:
                logger.info("性能监控被取消")
                break
            except Exception as e:
                logger.error(f"性能监控出错: {e}")

    async def _update_performance_metrics(self):
        """更新性能指标"""
        try:
            # 更新系统指标
            if hasattr(psutil, 'cpu_percent'):
                self._metrics.system_cpu_usage = psutil.cpu_percent(interval=None)
            if hasattr(psutil, 'virtual_memory'):
                self._metrics.system_memory_usage = psutil.virtual_memory().percent

            # 更新任务统计
            async with self._tasks_lock:
                active_tasks = sum(1 for task in self._tasks.values()
                                 if task.status == TaskStatus.RUNNING)
                self._metrics.current_active_tasks = active_tasks
                self._metrics.max_concurrent_tasks = max(
                    self._metrics.max_concurrent_tasks, active_tasks
                )

            # 更新事件循环利用率（简化计算）
            current_time = time.time()
            time_diff = current_time - self._last_metrics_update
            if time_diff > 0:
                # 基于活跃任务数估算事件循环利用率
                utilization = min(100.0, (active_tasks / max(1, self._cpu_count * 2)) * 100)
                self._metrics.event_loop_utilization = utilization

            self._last_metrics_update = current_time

        except Exception as e:
            logger.error(f"更新性能指标失败: {e}")

    async def _check_auto_scaling(self):
        """检查是否需要自动扩缩容"""
        try:
            # 简化的自动扩缩逻辑
            # 如果CPU使用率过高且有待处理任务，考虑扩容
            if (self._metrics.system_cpu_usage > 80 and
                self._metrics.current_active_tasks > self._cpu_count * 2):
                logger.info("检测到高负载，建议增加工作线程")

            # 如果CPU使用率很低且活跃任务少，考虑缩容
            elif (self._metrics.system_cpu_usage < 20 and
                  self._metrics.current_active_tasks < self._cpu_count):
                logger.info("检测到低负载，可以考虑减少工作线程")

        except Exception as e:
            logger.error(f"自动扩缩检查失败: {e}")

    def shutdown(self):
        """关闭异步管理器"""
        if not self._running:
            return

        logger.info("开始关闭高性能异步管理器...")

        # 停止性能监控
        if self._monitoring_task and not self._monitoring_task.done():
            self._monitoring_task.cancel()

        # 清理纯协程任务
        if self._coroutine_tasks:
            logger.info(f"清理 {len(self._coroutine_tasks)} 个纯协程任务")
            for task_id in list(self._coroutine_tasks.keys()):
                if task_id in self._interrupt_events:
                    self._interrupt_events[task_id].set()
            self._coroutine_tasks.clear()
            self._interrupt_events.clear()

        # 停止事件循环
        if self._loop and self._loop.is_running():
            self._loop.call_soon_threadsafe(self._loop.stop)

        # 等待事件循环线程结束
        if self._loop_thread and self._loop_thread.is_alive():
            self._loop_thread.join(timeout=3.0)

        # 关闭线程池
        self._io_thread_pool.shutdown(wait=True)
        self._cpu_thread_pool.shutdown(wait=True)
        self._process_pool.shutdown(wait=True)

        self._running = False

        # 输出最终性能报告
        if self._enable_monitoring:
            final_metrics = self._metrics.get_summary()
            logger.info(f"最终性能报告: {final_metrics}")

        logger.info("高性能异步管理器已关闭")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取当前性能指标"""
        return self._metrics.get_summary()

    def reset_metrics(self):
        """重置性能指标"""
        self._metrics = AsyncManagerMetrics()
        self._metrics.io_pool_metrics.max_workers = self._cpu_count * 4
        self._metrics.cpu_pool_metrics.max_workers = self._cpu_count * 2
        self._metrics.process_pool_metrics.max_workers = self._cpu_count
        logger.info("性能指标已重置")

    async def submit_priority_task(self, func: Callable, *args,
                                  priority: TaskPriority = TaskPriority.NORMAL,
                                  use_process_pool: bool = False,
                                  task_type: TaskType = TaskType.GENERAL,
                                  metadata: Dict = None, **kwargs) -> str:
        """
        提交优先级任务

        参数:
            func: 要执行的函数
            *args: 函数参数
            priority: 任务优先级
            use_process_pool: 是否使用进程池
            task_type: 任务类型
            metadata: 任务元数据
            **kwargs: 函数关键字参数

        返回:
            任务ID
        """
        task_id = self._generate_task_id()

        # 创建优先级任务
        priority_task = PriorityTask(
            priority=priority,
            timestamp=time.time(),
            task_id=task_id,
            func=func,
            args=args,
            kwargs=kwargs,
            is_coroutine=False
        )

        # 添加到优先级队列
        async with self._queue_lock:
            heapq.heappush(self._priority_queue, priority_task)

        # 创建任务结果对象
        task_result = create_task_result(
            task_id=task_id,
            task_type=task_type,
            status=TaskStatus.PENDING
        )
        if metadata:
            task_result.metadata.update(metadata)
        task_result.metadata['priority'] = priority.name

        async with self._tasks_lock:
            self._tasks[task_id] = task_result

        # 更新指标
        self._metrics.total_tasks_submitted += 1

        # 立即处理高优先级任务
        if priority in (TaskPriority.HIGH, TaskPriority.CRITICAL):
            await self._process_priority_task(priority_task, use_process_pool)
        else:
            # 启动任务处理器（如果还没有运行）
            asyncio.create_task(self._process_priority_queue())

        logger.info(f"提交优先级任务: {task_id}, 优先级: {priority.name}")
        return task_id

    async def _process_priority_queue(self):
        """处理优先级队列中的任务"""
        try:
            while True:
                async with self._queue_lock:
                    if not self._priority_queue:
                        break
                    priority_task = heapq.heappop(self._priority_queue)

                # 处理任务
                await self._process_priority_task(priority_task, use_process_pool=False)

                # 避免阻塞事件循环
                await asyncio.sleep(0)

        except Exception as e:
            logger.error(f"处理优先级队列失败: {e}")

    async def _process_priority_task(self, priority_task: PriorityTask, use_process_pool: bool = False):
        """处理单个优先级任务"""
        task_id = priority_task.task_id

        try:
            # 更新任务状态
            await self._update_task_status(task_id, TaskStatus.RUNNING)

            # 选择合适的执行器
            if use_process_pool:
                pool = self._process_pool
                pool_metrics = self._metrics.process_pool_metrics
            elif self._is_cpu_intensive_task(priority_task.func):
                pool = self._cpu_thread_pool
                pool_metrics = self._metrics.cpu_pool_metrics
            else:
                pool = self._io_thread_pool
                pool_metrics = self._metrics.io_pool_metrics

            # 更新池指标
            pool_metrics.pending_tasks += 1

            # 定义任务完成回调
            def task_done_callback(future):
                try:
                    execution_time = time.time() - priority_task.timestamp

                    if future.cancelled():
                        asyncio.run_coroutine_threadsafe(
                            self._update_task_status(task_id, TaskStatus.CANCELLED),
                            self._loop
                        )
                        self._metrics.total_tasks_cancelled += 1
                        pool_metrics.pending_tasks = max(0, pool_metrics.pending_tasks - 1)
                    elif future.exception():
                        asyncio.run_coroutine_threadsafe(
                            self._update_task_status(task_id, TaskStatus.FAILED, error=future.exception()),
                            self._loop
                        )
                        self._metrics.total_tasks_failed += 1
                        pool_metrics.failed_tasks += 1
                        pool_metrics.pending_tasks = max(0, pool_metrics.pending_tasks - 1)
                    else:
                        result = future.result()
                        asyncio.run_coroutine_threadsafe(
                            self._update_task_status(task_id, TaskStatus.COMPLETED, result=result),
                            self._loop
                        )
                        self._metrics.total_tasks_completed += 1
                        pool_metrics.completed_tasks += 1
                        pool_metrics.pending_tasks = max(0, pool_metrics.pending_tasks - 1)
                        pool_metrics.update_execution_time(execution_time)

                except Exception as e:
                    logger.error(f"任务回调处理失败: {task_id}, 错误: {e}")

            # 提交任务到线程池
            future = pool.submit(priority_task.func, *priority_task.args, **priority_task.kwargs)
            future.add_done_callback(task_done_callback)

            # 保存future到任务结果
            async with self._tasks_lock:
                if task_id in self._tasks:
                    self._tasks[task_id].future = future

        except Exception as e:
            logger.error(f"处理优先级任务失败: {task_id}, 错误: {e}")
            await self._update_task_status(task_id, TaskStatus.FAILED, error=e)

    def _is_cpu_intensive_task(self, func: Callable) -> bool:
        """判断是否为CPU密集型任务"""
        # 简化的启发式判断
        func_name = getattr(func, '__name__', str(func))
        cpu_intensive_keywords = ['hash', 'compute', 'calculate', 'process', 'analyze', 'compress']
        return any(keyword in func_name.lower() for keyword in cpu_intensive_keywords)

    async def get_queue_status(self) -> Dict[str, Any]:
        """获取队列状态"""
        async with self._queue_lock:
            queue_size = len(self._priority_queue)
            priority_distribution = {}
            for task in self._priority_queue:
                priority_name = task.priority.name
                priority_distribution[priority_name] = priority_distribution.get(priority_name, 0) + 1

        return {
            'queue_size': queue_size,
            'priority_distribution': priority_distribution,
            'metrics': self.get_performance_metrics()
        }


# 向后兼容性类
class AsyncManager(OptimizedAsyncManager):
    """
    向后兼容的异步管理器类

    这个类继承自 OptimizedAsyncManager，提供与原始 AsyncManager 类相同的接口，
    同时享受新的高性能实现带来的性能提升。
    """

    def __init__(self):
        """
        初始化异步管理器（向后兼容）
        """
        # 调用优化版本的初始化，使用默认设置
        super().__init__(enable_monitoring=True, auto_scale=True)

    # 保留原有的方法签名以确保兼容性
    async def submit_task(self, func: Callable, *args, use_process_pool: bool = False,
                         task_type: TaskType = TaskType.GENERAL,
                         metadata: Dict = None, **kwargs) -> str:
        """
        提交任务（兼容性接口）

        自动将任务转换为优先级任务，使用NORMAL优先级
        """
        # 确保在正确的事件循环中运行
        if self._loop and self._loop != asyncio.get_running_loop():
            # 如果不在管理器的事件循环中，使用 run_coroutine_threadsafe
            future = asyncio.run_coroutine_threadsafe(
                self.submit_priority_task(
                    func, *args,
                    priority=TaskPriority.NORMAL,
                    use_process_pool=use_process_pool,
                    task_type=task_type,
                    metadata=metadata,
                    **kwargs
                ),
                self._loop
            )
            return future.result()
        else:
            return await self.submit_priority_task(
                func, *args,
                priority=TaskPriority.NORMAL,
                use_process_pool=use_process_pool,
                task_type=task_type,
                metadata=metadata,
                **kwargs
            )

    async def submit_pure_coroutine(self, task_id: Optional[str], coro: Any,
                                   task_type: TaskType = TaskType.GENERAL,
                                   interrupt_event: Optional[asyncio.Event] = None) -> str:
        """
        提交纯协程任务（合并自AsyncTaskManager）

        参数:
            task_id: 任务ID，如果为None则自动生成
            coro: 协程对象
            task_type: 任务类型
            interrupt_event: 可选的外部中断事件

        返回:
            任务ID
        """
        async with self._tasks_lock:
            # 如果task_id为None，自动生成一个
            if task_id is None:
                task_id = self._generate_task_id()

            if task_id in self._coroutine_tasks:
                logger.warning(f"纯协程任务ID已存在: {task_id}")
                return task_id

            # 创建任务结果
            task_result = create_task_result(
                task_id=task_id,
                task_type=task_type,
                status=TaskStatus.PENDING
            )
            task_result.start_time = time.time()
            self._coroutine_tasks[task_id] = task_result

            # 创建或使用外部中断事件
            self._interrupt_events[task_id] = interrupt_event or asyncio.Event()

            logger.info(f"提交纯协程任务: {task_id}")

            # 创建任务并开始执行
            async def task_wrapper():
                try:
                    # 更新状态为运行中
                    task_result.status = TaskStatus.RUNNING

                    # 执行协程
                    result = await coro

                    # 更新结果
                    task_result.result = result
                    task_result.status = TaskStatus.COMPLETED
                    task_result.end_time = time.time()

                    logger.info(f"纯协程任务完成: {task_id}")

                except asyncio.CancelledError:
                    task_result.status = TaskStatus.CANCELLED
                    task_result.end_time = time.time()
                    logger.info(f"纯协程任务被取消: {task_id}")
                    raise
                except Exception as e:
                    task_result.error = str(e)
                    task_result.status = TaskStatus.FAILED
                    task_result.end_time = time.time()
                    logger.error(f"纯协程任务失败: {task_id}, 错误: {e}")

            # 在事件循环中创建任务
            asyncio.create_task(task_wrapper())

        return task_id

    async def submit_coroutine(self, coro, task_type: TaskType = TaskType.GENERAL,
                              metadata: Dict = None) -> str:
        """
        提交协程任务（兼容性接口）
        """
        # 简化的协程提交实现
        task_id = self._generate_task_id()

        # 创建任务结果对象
        task_result = create_task_result(
            task_id=task_id,
            task_type=task_type,
            status=TaskStatus.PENDING
        )
        if metadata:
            task_result.metadata.update(metadata)

        # 确保在正确的事件循环中运行
        if self._loop and self._loop != asyncio.get_running_loop():
            # 如果不在管理器的事件循环中，使用 run_coroutine_threadsafe
            future = asyncio.run_coroutine_threadsafe(
                self._submit_coroutine_internal(task_id, coro, task_result),
                self._loop
            )
            return future.result()
        else:
            return await self._submit_coroutine_internal(task_id, coro, task_result)

    async def _submit_coroutine_internal(self, task_id: str, coro, task_result):
        """内部协程提交方法"""
        async with self._tasks_lock:
            self._tasks[task_id] = task_result

        async def task_wrapper():
            await self._update_task_status(task_id, TaskStatus.RUNNING)
            try:
                result = await coro
                await self._update_task_status(task_id, TaskStatus.COMPLETED, result=result)
                return result
            except asyncio.CancelledError:
                await self._update_task_status(task_id, TaskStatus.CANCELLED)
                raise
            except Exception as e:
                await self._update_task_status(task_id, TaskStatus.FAILED, error=e)
                raise

        task = self._loop.create_task(task_wrapper())

        async with self._tasks_lock:
            if task_id in self._tasks:
                self._tasks[task_id].future = task

        return task_id

    def get_event_loop(self):
        """获取当前事件循环"""
        if self._loop and self._loop.is_running():
            return self._loop
        else:
            logger.warning("AsyncManager的事件循环未运行")
            return None

    def _generate_task_id(self) -> str:
        return str(uuid.uuid4())
    
    async def _update_task_status(self, task_id: str, status: TaskStatus, 
                                 result: Any = None, error: Exception = None):
        async with self._tasks_lock:
            if task_id in self._tasks:
                task_result = self._tasks[task_id]
                task_result.status = status
                
                if status == TaskStatus.RUNNING and task_result.start_time is None:
                    task_result.start_time = time.time()
                
                if status in (TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED):
                    task_result.end_time = time.time()
                
                if result is not None:
                    task_result.result = result
                
                if error is not None:
                    task_result.error = error
                
                event_system.publish(f"task_status_changed", {
                    "task_id": task_id,
                    "status": status,
                    "task_result": task_result
                })
    

    async def interrupt_pure_coroutine(self, task_id: str) -> bool:
        """
        中断纯协程任务（合并自AsyncTaskManager）
        """
        async with self._tasks_lock:
            if task_id not in self._coroutine_tasks:
                logger.warning(f"纯协程任务不存在: {task_id}")
                return False

            # 设置中断事件
            if task_id in self._interrupt_events:
                self._interrupt_events[task_id].set()
                logger.info(f"已设置纯协程任务中断信号: {task_id}")

            # 更新任务状态
            task_result = self._coroutine_tasks[task_id]
            if task_result.status == TaskStatus.RUNNING:
                task_result.status = TaskStatus.CANCELLED
                task_result.end_time = time.time()
                logger.info(f"纯协程任务已中断: {task_id}")

            return True

    async def get_pure_coroutine_result(self, task_id: str) -> Optional[UnifiedTaskResult]:
        """
        获取纯协程任务结果（合并自AsyncTaskManager）
        """
        async with self._tasks_lock:
            return self._coroutine_tasks.get(task_id)

    async def wait_for_pure_coroutine(self, task_id: str, timeout: Optional[float] = None) -> Optional[UnifiedTaskResult]:
        """
        等待纯协程任务完成（合并自AsyncTaskManager）
        """
        start_time = time.time()
        while True:
            if timeout is not None and time.time() - start_time > timeout:
                logger.warning(f"等待纯协程任务超时: {task_id}")
                return None

            task_result = await self.get_pure_coroutine_result(task_id)
            if task_result and task_result.status in (TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED):
                return task_result

            await asyncio.sleep(0.1)

    async def cancel_task(self, task_id: str) -> bool:
        """
        取消任务（支持普通任务和纯协程任务）
        """
        # 首先检查是否是纯协程任务
        if task_id in self._coroutine_tasks:
            return await self.interrupt_pure_coroutine(task_id)

        # 处理普通任务
        async with self._tasks_lock:
            if task_id not in self._tasks:
                return False

            task_result = self._tasks[task_id]

            if task_result.status in (TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED):
                return False

            future = task_result.future
            if future:
                if isinstance(future, asyncio.Task):
                    if not future.done():
                        future.cancel()
                        return True
                elif isinstance(future, concurrent.futures.Future):
                    if not future.done():
                        return future.cancel()

            # 如果无法取消，只更新状态
            await self._update_task_status(task_id, TaskStatus.CANCELLED)
            return True

    async def get_task_result(self, task_id: str) -> Optional[UnifiedTaskResult]:
        """
        获取任务结果（支持普通任务和纯协程任务）
        """
        # 首先检查是否是纯协程任务
        if task_id in self._coroutine_tasks:
            return await self.get_pure_coroutine_result(task_id)

        # 处理普通任务
        async with self._tasks_lock:
            return self._tasks.get(task_id)

    async def wait_for_task(self, task_id: str, timeout: Optional[float] = None) -> Optional[UnifiedTaskResult]:
        """
        等待任务完成（支持普通任务和纯协程任务）
        """
        # 首先检查是否是纯协程任务
        if task_id in self._coroutine_tasks:
            return await self.wait_for_pure_coroutine(task_id, timeout)

        # 处理普通任务
        start_time = time.time()
        while True:
            if timeout is not None and time.time() - start_time > timeout:
                return None

            task_result = await self.get_task_result(task_id)
            if task_result is None:
                return None

            if task_result.status in (TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED):
                return task_result

            await asyncio.sleep(0.01)

    async def get_all_tasks(self, status: Optional[TaskStatus] = None) -> Dict[str, UnifiedTaskResult]:
        """
        获取所有任务（包括普通任务和纯协程任务）
        """
        async with self._tasks_lock:
            # 合并普通任务和纯协程任务
            all_tasks = self._tasks.copy()
            all_tasks.update(self._coroutine_tasks)

            if status is None:
                return all_tasks

            return {task_id: task_result for task_id, task_result in all_tasks.items()
                    if task_result.status == status}

    async def clear_completed_tasks(self, clear_failed: bool = True, clear_cancelled: bool = True) -> int:
        statuses_to_clear = {TaskStatus.COMPLETED}
        if clear_failed:
            statuses_to_clear.add(TaskStatus.FAILED)
        if clear_cancelled:
            statuses_to_clear.add(TaskStatus.CANCELLED)
        
        async with self._tasks_lock:
            task_ids_to_clear = [
                task_id for task_id, task in self._tasks.items() 
                if task.status in statuses_to_clear
            ]
            for task_id in task_ids_to_clear:
                del self._tasks[task_id]
        
        return len(task_ids_to_clear)

    def run_in_thread(self, func: Callable, *args, **kwargs) -> concurrent.futures.Future:
        """
        在线程池中运行函数
        
        参数:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        返回:
            Future对象
        """
        return self._io_thread_pool.submit(func, *args, **kwargs) # 默认提交到IO线程池
    
    def run_in_process(self, func: Callable, *args, **kwargs) -> concurrent.futures.Future:
        """
        在进程池中运行函数
        
        参数:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        返回:
            Future对象
        """
        return self._process_pool.submit(func, *args, **kwargs)
    
    def run_coroutine(self, coro) -> Any:
        """
        运行协程并等待结果
        
        参数:
            coro: 协程对象
            
        返回:
            协程执行结果
        """
        future = asyncio.run_coroutine_threadsafe(coro, asyncio.get_running_loop())
        return future.result()
    
    async def schedule_task(self, func: Callable, delay: float, *args, 
                      is_coroutine: bool = False, **kwargs) -> str:
        """
        调度任务在指定延迟后执行
        
        参数:
            func: 要执行的函数或协程函数
            delay: 延迟时间（秒）
            *args: 函数参数
            is_coroutine: 是否为协程函数
            **kwargs: 函数关键字参数
            
        返回:
            任务ID
        """
        task_id = self._generate_task_id()
        
        # 创建任务结果对象
        task_result = TaskResult(
            task_id=task_id,
            status=TaskStatus.PENDING,
            metadata={"scheduled_time": time.time() + delay}
        )
        
        # 保存任务
        async with self._tasks_lock:
            self._tasks[task_id] = task_result
        
        # 定义延迟执行函数
        async def delayed_execution():
            # 异步等待指定的延迟时间
            await asyncio.sleep(delay)
            
            # 如果是协程函数
            if is_coroutine:
                # 创建协程对象
                coro = func(*args, **kwargs)
                # 提交协程任务
                return await self.submit_coroutine(coro, metadata={"parent_task_id": task_id})
            else:
                # 提交普通任务
                return await self.submit_task(func, *args, metadata={"parent_task_id": task_id}, **kwargs)
        
        # 在事件循环中创建任务
        loop = asyncio.get_running_loop()
        task = asyncio.run_coroutine_threadsafe(delayed_execution(), loop)
        
        # 保存任务对象到元数据
        async with self._tasks_lock:
            if task_id in self._tasks:
                self._tasks[task_id].metadata["scheduler_task"] = task
        
        logger.info(f"调度任务: {task_id}, 延迟: {delay}秒")
        return task_id
    
    async def schedule_task_async(self, func: Callable, delay: float, *args, 
                                is_coroutine: bool = False, **kwargs) -> str:
        """
        异步调度任务在指定延迟后执行
        
        参数:
            func: 要执行的函数或协程函数
            delay: 延迟时间（秒）
            *args: 函数参数
            is_coroutine: 是否为协程函数
            **kwargs: 函数关键字参数
            
        返回:
            任务ID
        """
        task_id = self._generate_task_id()
        
        # 创建任务结果对象
        task_result = TaskResult(
            task_id=task_id,
            status=TaskStatus.PENDING,
            metadata={"scheduled_time": time.time() + delay}
        )
        
        # 保存任务
        async with self._tasks_lock:
            self._tasks[task_id] = task_result
        
        # 定义异步延迟执行函数
        async def async_delayed_execution():
            # 异步等待指定的延迟时间
            await asyncio.sleep(delay)
            
            # 如果是协程函数
            if is_coroutine:
                # 创建协程对象
                coro = func(*args, **kwargs)
                # 提交协程任务
                return await self.submit_coroutine(coro, metadata={"parent_task_id": task_id})
            else:
                # 提交普通任务
                return await self.submit_task(func, *args, metadata={"parent_task_id": task_id}, **kwargs)
        
        # 在AsyncManager的事件循环中创建任务
        if not self._loop or not self._loop.is_running():
            raise RuntimeError("AsyncManager事件循环未运行")

        task = asyncio.run_coroutine_threadsafe(async_delayed_execution(), self._loop)
        
        # 保存任务对象到元数据
        async with self._tasks_lock:
            if task_id in self._tasks:
                self._tasks[task_id].metadata["scheduler_task"] = task
        
        logger.info(f"异步调度任务: {task_id}, 延迟: {delay}秒")
        return task_id

    def get_event_loop(self):
        """获取AsyncManager的事件循环"""
        if self._loop and self._loop.is_running():
            return self._loop
        else:
            logger.warning("AsyncManager事件循环未运行")
            return None

    def is_available(self):
        """检查异步管理器是否可用"""
        try:
            loop = self.get_event_loop()
            return loop is not None and loop.is_running()
        except Exception:
            return False

    def run_coroutine_sync(self, coro) -> Any:
        """在统一事件循环中同步执行协程

        Args:
            coro: 要执行的协程

        Returns:
            协程的执行结果

        Raises:
            RuntimeError: 如果AsyncManager事件循环未运行
        """
        if self._loop and self._loop.is_running():
            future = asyncio.run_coroutine_threadsafe(coro, self._loop)
            return future.result()
        else:
            raise RuntimeError("AsyncManager事件循环未运行")

    async def submit_cpu_task(self, func: Callable, *args, **kwargs) -> str:
        """提交CPU密集型任务（使用asyncio.to_thread）

        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数

        Returns:
            str: 任务ID
        """
        task_id = self._generate_task_id()

        # 使用asyncio.to_thread而不是线程池
        async def cpu_task_wrapper():
            return await asyncio.to_thread(func, *args, **kwargs)

        # 创建任务结果对象
        task_result = create_task_result(
            task_id=task_id,
            task_type=TaskType.GENERAL,
            status=TaskStatus.PENDING
        )

        # 在事件循环中执行
        task = asyncio.create_task(cpu_task_wrapper())
        task.add_done_callback(
            lambda t: self._handle_task_completion(task_id, t)
        )

        async with self._tasks_lock:
            self._tasks[task_id] = task_result

        logger.info(f"提交CPU密集型任务: {task_id}")
        return task_id

    async def submit_batch_task(self, func: Callable, items: List,
                               batch_size: int = 100, task_type: TaskType = TaskType.GENERAL) -> str:
        """提交批量任务（统一批处理接口）

        Args:
            func: 处理单个项目的函数
            items: 要处理的项目列表
            batch_size: 批处理大小
            task_type: 任务类型

        Returns:
            str: 任务ID
        """
        task_id = self._generate_task_id()

        async def batch_task_wrapper():
            results = []
            for i in range(0, len(items), batch_size):
                batch = items[i:i+batch_size]
                # 使用asyncio.to_thread处理每个批次
                batch_tasks = [
                    asyncio.to_thread(func, item) for item in batch
                ]
                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

                # 过滤异常结果
                valid_results = [r for r in batch_results if not isinstance(r, Exception)]
                results.extend(valid_results)

                # 让出控制权
                await asyncio.sleep(0)

            return results

        # 创建并执行任务
        task = asyncio.create_task(batch_task_wrapper())
        task_result = create_task_result(
            task_id=task_id,
            task_type=task_type,
            status=TaskStatus.RUNNING
        )

        task.add_done_callback(
            lambda t: self._handle_task_completion(task_id, t)
        )

        async with self._tasks_lock:
            self._tasks[task_id] = task_result

        logger.info(f"提交批量任务: {task_id}, 项目数: {len(items)}, 批次大小: {batch_size}")
        return task_id

    def _handle_task_completion(self, task_id: str, task: asyncio.Task):
        """处理任务完成回调"""
        try:
            if task_id in self._tasks:
                task_result = self._tasks[task_id]

                if task.cancelled():
                    task_result.status = TaskStatus.CANCELLED
                    logger.info(f"任务被取消: {task_id}")
                elif task.exception():
                    task_result.status = TaskStatus.FAILED
                    task_result.error = str(task.exception())
                    logger.error(f"任务执行失败: {task_id}, 错误: {task.exception()}")
                else:
                    task_result.status = TaskStatus.COMPLETED
                    task_result.result = task.result()
                    logger.info(f"任务执行完成: {task_id}")

                task_result.end_time = time.time()

        except Exception as e:
            logger.error(f"处理任务完成回调失败: {task_id}, 错误: {e}")

    async def submit_batch_tasks(self, tasks: List[Dict], batch_id: Optional[str] = None, 
                          use_process_pool: bool = False, **kwargs) -> str:
        """
        提交批量任务
        
        参数:
            tasks: 任务列表，每个任务包含 func, args, kwargs
            batch_id: 批量任务ID，如果为None则自动生成
            use_process_pool: 是否使用进程池
            **kwargs: 传递给所有任务的通用参数
            
        返回:
            批量任务ID
        """
        if not tasks:
            raise ValueError("任务列表不能为空")
        
        batch_id = batch_id or f"batch_{self._generate_task_id()}"
        sub_task_ids = []
        
        # 创建批量任务记录
        async with self._batch_lock:
            self._batch_tasks[batch_id] = sub_task_ids
        
        # 提交所有子任务
        for i, task_info in enumerate(tasks):
            func = task_info['func']
            args = task_info.get('args', [])
            task_kwargs = task_info.get('kwargs', {})
            
            # 合并通用参数
            merged_kwargs = {**kwargs, **task_kwargs}
            
            # 提交子任务
            sub_task_id = await self.submit_task(
                func, *args, 
                use_process_pool=use_process_pool,
                metadata={
                    'batch_id': batch_id,
                    'batch_index': i,
                    'batch_total': len(tasks),
                    **task_info.get('metadata', {})
                },
                **merged_kwargs
            )
            
            sub_task_ids.append(sub_task_id)
        
        logger.info(f"批量任务提交完成: {batch_id}, 包含 {len(tasks)} 个子任务")
        return batch_id
    
    async def get_batch_progress(self, batch_id: str) -> Dict[str, Any]:
        """
        获取批量任务进度
        
        参数:
            batch_id: 批量任务ID
            
        返回:
            批量任务进度信息
        """
        async with self._batch_lock:
            if batch_id not in self._batch_tasks:
                return {'error': f'批量任务不存在: {batch_id}'}
            
            sub_task_ids = self._batch_tasks[batch_id]
        
        # 统计子任务状态
        total = len(sub_task_ids)
        completed = 0
        running = 0
        failed = 0
        pending = 0
        
        for task_id in sub_task_ids:
            task_result = await self.get_task_result(task_id)
            if task_result:
                if task_result.status == TaskStatus.COMPLETED:
                    completed += 1
                elif task_result.status == TaskStatus.RUNNING:
                    running += 1
                elif task_result.status == TaskStatus.FAILED:
                    failed += 1
                elif task_result.status == TaskStatus.PENDING:
                    pending += 1
        
        progress = (completed / total * 100) if total > 0 else 0
        
        return {
            'batch_id': batch_id,
            'total': total,
            'completed': completed,
            'running': running,
            'failed': failed,
            'pending': pending,
            'progress': progress,
            'sub_tasks': sub_task_ids
        }
    
    async def wait_for_batch_completion(self, batch_id: str, timeout: Optional[float] = None) -> Dict[str, Any]:
        """
        等待批量任务完成
        
        参数:
            batch_id: 批量任务ID
            timeout: 超时时间（秒）
            
        返回:
            批量任务完成结果
        """
        async with self._batch_lock:
            if batch_id not in self._batch_tasks:
                return {'error': f'批量任务不存在: {batch_id}'}
            
            sub_task_ids = self._batch_tasks[batch_id]
        
        # 等待所有子任务完成
        start_time = time.time()
        results = []
        
        for task_id in sub_task_ids:
            if timeout:
                remaining_timeout = timeout - (time.time() - start_time)
                if remaining_timeout <= 0:
                    return {'error': '批量任务等待超时'}
                
                task_result = await self.wait_for_task(task_id, remaining_timeout)
            else:
                task_result = await self.wait_for_task(task_id)
            
            if task_result:
                results.append(task_result)
        
        return {
            'batch_id': batch_id,
            'completed': len(results),
            'total': len(sub_task_ids),
            'results': results
        }
    
    async def submit_cpu_intensive_task(self, func: Callable, *args, **kwargs) -> str:
        """
        提交CPU密集型任务到CPU线程池
        
        参数:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数
            
        返回:
            任务ID
        """
        task_id = self._generate_task_id()
        
        # 创建任务结果对象
        task_result = TaskResult(
            task_id=task_id,
            status=TaskStatus.PENDING,
            metadata=kwargs.get('metadata', {})
        )
        
        # 保存任务
        async with self._tasks_lock:
            self._tasks[task_id] = task_result
        
        # 定义任务完成回调
        def task_done_callback(future):
            try:
                loop = asyncio.get_running_loop()
                result = future.result()
                asyncio.run_coroutine_threadsafe(self._update_task_status(task_id, TaskStatus.COMPLETED, result=result), loop)
                logger.info(f"CPU密集型任务完成: {task_id}")
            except concurrent.futures.CancelledError:
                loop = asyncio.get_running_loop()
                asyncio.run_coroutine_threadsafe(self._update_task_status(task_id, TaskStatus.CANCELLED), loop)
                logger.info(f"CPU密集型任务取消: {task_id}")
            except Exception as e:
                loop = asyncio.get_running_loop()
                asyncio.run_coroutine_threadsafe(self._update_task_status(task_id, TaskStatus.FAILED, error=e), loop)
                logger.error(f"CPU密集型任务失败: {task_id}, 错误: {e}")
        
        # 更新任务状态为运行中
        await self._update_task_status(task_id, TaskStatus.RUNNING)
        
        # 提交到CPU线程池
        future = self._cpu_thread_pool.submit(func, *args, **kwargs)
        future.add_done_callback(task_done_callback)
        
        logger.info(f"提交CPU密集型任务: {task_id}")
        return task_id


# 获取异步管理器实例
def get_async_manager() -> AsyncManager:
    """
    获取异步管理器实例

    返回:
        AsyncManager: 异步管理器实例（高性能版本）
    """
    return AsyncManager()


def get_optimized_async_manager(enable_monitoring: bool = True, auto_scale: bool = True) -> OptimizedAsyncManager:
    """
    获取优化的异步管理器实例

    参数:
        enable_monitoring: 是否启用性能监控
        auto_scale: 是否启用自动扩缩容

    返回:
        OptimizedAsyncManager: 优化的异步管理器实例
    """
    return OptimizedAsyncManager(enable_monitoring=enable_monitoring, auto_scale=auto_scale)


# 测试代码
if __name__ == "__main__":
    async def main():
        # 创建异步管理器
        async_manager = get_async_manager()
        
        # 定义测试函数
        def test_function(x, y):
            print(f"执行测试函数: {x} + {y} = {x + y}")
            time.sleep(1) # 模拟阻塞
            return x + y
        
        # 定义测试协程
        async def test_coroutine(x, y):
            print(f"执行测试协程: {x} * {y} = {x * y}")
            await asyncio.sleep(1)  # 模拟异步操作
            return x * y
        
        # 提交同步任务
        task_id1 = await async_manager.submit_task(test_function, 10, 20)
        print(f"提交同步任务: {task_id1}")
        
        # 提交协程任务
        task_id2 = await async_manager.submit_coroutine(test_coroutine(5, 6))
        print(f"提交协程任务: {task_id2}")
        
        # 等待任务完成
        result1 = await async_manager.wait_for_task(task_id1)
        print(f"同步任务结果: {result1.result}")
        
        result2 = await async_manager.wait_for_task(task_id2)
        print(f"协程任务结果: {result2.result}")
        
        # 调度任务
        task_id3 = await async_manager.schedule_task_async(test_function, 2, 30, 40)
        print(f"调度任务: {task_id3}")
        
        # 等待一段时间，让调度任务执行
        await asyncio.sleep(3)
        
        # 关闭异步管理器
        async_manager.shutdown()

    asyncio.run(main())