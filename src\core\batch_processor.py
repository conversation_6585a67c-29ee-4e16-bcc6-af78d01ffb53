#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
批量处理器模块

该模块提供批量文件处理和视频转换功能：
1. 批量文件扫描和处理
2. 批量视频编码转换
3. 任务进度管理和状态跟踪
4. 支持多线程和多进程处理

作者: AI助手
日期: 2024-01-01
版本: 1.0.0
"""

import os
import threading
import time
import asyncio
from typing import List, Dict, Any, Optional, Callable, TypeVar, Generic, Tuple
from concurrent.futures import ThreadPoolExecutor

from .interfaces import BatchProcessorInterface, InterruptibleInterface
from .hash_calculator import HashCalculator, MultiLevelHashCalculator
from .file_classifier import FileClassifier
from .progress_tracker import ProgressTracker, MultiStageProgressTracker
from .rule_engine import RuleEngine
from .whitelist_service import WhitelistService

from src.utils.logger import get_logger
logger = get_logger(__name__)

T = TypeVar('T')
R = TypeVar('R')

class InterruptibleTask(InterruptibleInterface):
    """可中断任务的基本实现"""
    
    def __init__(self):
        self._interrupted = False
        self._lock = threading.Lock()
    
    def set_interrupted(self) -> None:
        """设置中断标志"""
        with self._lock:
            self._interrupted = True
    
    def check_interrupted(self) -> bool:
        """检查是否中断
        
        Returns:
            bool: 是否已中断
        """
        with self._lock:
            return self._interrupted
    
    def reset_interrupted(self) -> None:
        """重置中断标志"""
        with self._lock:
            self._interrupted = False


class BatchProcessor(BatchProcessorInterface[T, R]):
    """批处理处理器实现"""
    
    def __init__(self, processor_func: Callable[[T], R], max_workers: Optional[int] = None):
        """初始化批处理处理器
        
        Args:
            processor_func: 处理单个项目的函数
            max_workers: 最大工作线程数，默认为CPU核心数
        """
        super().__init__()
        self._processor_func = processor_func
        self._max_workers = max_workers or os.cpu_count() or 4
        self._interrupted = False
        self._lock = threading.Lock()
        
        logger.info(f"批量处理器初始化完成，最大工作线程数: {self._max_workers}")
    
    def set_interrupted(self) -> None:
        """设置中断标志"""
        with self._lock:
            self._interrupted = True
    
    def check_interrupted(self) -> bool:
        """检查是否中断
        
        Returns:
            bool: 是否已中断
        """
        with self._lock:
            return self._interrupted
    
    def reset_interrupted(self) -> None:
        """重置中断标志"""
        with self._lock:
            self._interrupted = False
    
    async def process_batch_async(self, items: List[T]) -> List[R]:
        """异步处理一批项目

        Args:
            items: 要处理的项目列表

        Returns:
            List[R]: 处理结果列表
        """
        if not items:
            return []

        logger.debug(f"开始异步处理批次，共 {len(items)} 个项目")

        # 使用asyncio.to_thread处理CPU密集型任务
        tasks = []
        for item in items:
            task = asyncio.to_thread(self._processor_func, item)
            tasks.append(task)

        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 过滤异常结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"处理项目失败: {items[i]}, 错误: {result}")
            else:
                valid_results.append(result)

        logger.debug(f"异步批次处理完成，成功处理 {len(valid_results)} 个项目")
        return valid_results

    def process_batch(self, items: List[T]) -> List[R]:
        """处理一批项目（同步兼容接口）

        Args:
            items: 要处理的项目列表

        Returns:
            List[R]: 处理结果列表
        """
        if not items:
            return []

        # 通过AsyncManager执行异步方法
        try:
            from src.utils.async_manager import get_async_manager
            async_manager = get_async_manager()

            # 如果AsyncManager可用，使用异步方法
            if async_manager.is_available():
                future = asyncio.run_coroutine_threadsafe(
                    self.process_batch_async(items),
                    async_manager.get_event_loop()
                )
                return future.result()
            else:
                # 回退到原有的ThreadPoolExecutor方式
                logger.warning("AsyncManager不可用，使用ThreadPoolExecutor回退方案")
                with ThreadPoolExecutor(max_workers=self._max_workers) as executor:
                    results = list(executor.map(self._processor_func, items))
                return results

        except Exception as e:
            logger.error(f"异步批处理失败，使用ThreadPoolExecutor回退方案: {e}")
            # 回退到原有方式
            with ThreadPoolExecutor(max_workers=self._max_workers) as executor:
                results = list(executor.map(self._processor_func, items))
            return results
    
    async def process_with_interruption_check_async(self, items: List[T], batch_size: int = 100) -> List[R]:
        """带中断检查的异步批处理

        Args:
            items: 要处理的项目列表
            batch_size: 批处理大小

        Returns:
            List[R]: 处理结果列表
        """
        if not items:
            return []

        logger.info(f"开始带中断检查的异步批处理，共 {len(items)} 个项目，批次大小: {batch_size}")

        results = []
        total_batches = (len(items) + batch_size - 1) // batch_size

        # 重置中断标志
        self.reset_interrupted()

        for i in range(0, len(items), batch_size):
            # 检查中断标志
            if self.check_interrupted():
                logger.warning(f"异步批处理被中断，已处理 {len(results)} 个项目")
                break

            # 获取当前批次
            batch = items[i:i+batch_size]
            batch_num = i // batch_size + 1

            logger.debug(f"处理批次 {batch_num}/{total_batches}，共 {len(batch)} 个项目")

            # 异步处理当前批次
            batch_results = await self.process_batch_async(batch)
            results.extend(batch_results)

            # 让出控制权
            await asyncio.sleep(0)

        logger.info(f"异步批处理完成，总共处理 {len(results)} 个项目")
        return results

    def process_with_interruption_check(self, items: List[T], batch_size: int = 100) -> List[R]:
        """带中断检查的批处理（同步兼容接口）

        Args:
            items: 要处理的项目列表
            batch_size: 批处理大小

        Returns:
            List[R]: 处理结果列表
        """
        if not items:
            return []

        # 尝试使用异步方法
        try:
            from src.utils.async_manager import get_async_manager
            async_manager = get_async_manager()

            if async_manager.is_available():
                future = asyncio.run_coroutine_threadsafe(
                    self.process_with_interruption_check_async(items, batch_size),
                    async_manager.get_event_loop()
                )
                return future.result()
            else:
                # 回退到原有实现
                return self._process_with_interruption_check_sync(items, batch_size)

        except Exception as e:
            logger.error(f"异步批处理失败，使用同步回退方案: {e}")
            return self._process_with_interruption_check_sync(items, batch_size)

    def _process_with_interruption_check_sync(self, items: List[T], batch_size: int = 100) -> List[R]:
        """同步批处理实现（回退方案）"""
        logger.info(f"开始带中断检查的批处理，共 {len(items)} 个项目，批次大小: {batch_size}")

        results = []
        total_batches = (len(items) + batch_size - 1) // batch_size

        # 重置中断标志
        self.reset_interrupted()

        for i in range(0, len(items), batch_size):
            # 检查中断标志
            if self.check_interrupted():
                logger.warning(f"批处理被中断，已处理 {len(results)} 个项目")
                break

            # 获取当前批次
            batch = items[i:i+batch_size]
            batch_num = i // batch_size + 1

            logger.debug(f"处理批次 {batch_num}/{total_batches}，共 {len(batch)} 个项目")

            # 处理当前批次
            with ThreadPoolExecutor(max_workers=self._max_workers) as executor:
                batch_results = list(executor.map(self._processor_func, batch))
            results.extend(batch_results)

        logger.info(f"批处理完成，总共处理 {len(results)} 个项目")
        return results


class FileHashBatchProcessor(BatchProcessor[str, Tuple[str, str]]):
    """文件哈希批处理器"""
    
    def __init__(self, hash_calculator, max_workers: Optional[int] = None):
        """初始化文件哈希批处理器
        
        Args:
            hash_calculator: 哈希计算器
            max_workers: 最大工作线程数
        """
        self.hash_calculator = hash_calculator
        super().__init__(self._calculate_file_hash, max_workers)
        logger.info("文件哈希批处理器初始化完成")
    
    def _calculate_file_hash(self, file_path: str) -> Tuple[str, str]:
        """计算文件哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[str, str]: (文件路径, 哈希值)
        """
        try:
            hash_value = self.hash_calculator.calculate_hash(file_path)
            return (file_path, hash_value)
        except Exception as e:
            logger.error(f"计算文件哈希值失败: {file_path}, 错误: {str(e)}")
            return (file_path, "")


class AdaptiveBatchProcessor(BatchProcessor[T, R]):
    """自适应批处理处理器"""
    
    def __init__(self, processor_func: Callable[[T], R], 
                 resource_monitor,
                 min_batch_size: int = 50, 
                 max_batch_size: int = 500,
                 min_workers: int = 1,
                 max_workers: Optional[int] = None):
        """初始化自适应批处理处理器
        
        Args:
            processor_func: 处理单个项目的函数
            resource_monitor: 资源监控器
            min_batch_size: 最小批处理大小
            max_batch_size: 最大批处理大小
            min_workers: 最小工作线程数
            max_workers: 最大工作线程数
        """
        super().__init__(processor_func, max_workers)
        self.resource_monitor = resource_monitor
        self.min_batch_size = min_batch_size
        self.max_batch_size = max_batch_size
        self.current_batch_size = (min_batch_size + max_batch_size) // 2
        self.min_workers = min_workers
        self.max_workers = max_workers or os.cpu_count() or 4
        self.current_workers = self.max_workers
    
    def adjust_batch_size(self) -> int:
        """根据系统资源调整批处理大小
        
        Returns:
            int: 调整后的批处理大小
        """
        cpu_usage = self.resource_monitor.get_cpu_usage()
        memory_usage = self.resource_monitor.get_memory_usage()
        
        # 根据CPU和内存使用率调整批处理大小
        if cpu_usage > 80 or memory_usage > 80:
            # 高负载，减小批处理大小
            self.current_batch_size = max(
                self.min_batch_size,
                int(self.current_batch_size * 0.8)
            )
        elif cpu_usage < 40 and memory_usage < 60:
            # 低负载，增加批处理大小
            self.current_batch_size = min(
                self.max_batch_size,
                int(self.current_batch_size * 1.2)
            )
        
        return self.current_batch_size
    
    def adjust_workers(self) -> int:
        """根据系统资源调整工作线程数
        
        Returns:
            int: 调整后的工作线程数
        """
        cpu_usage = self.resource_monitor.get_cpu_usage()
        memory_usage = self.resource_monitor.get_memory_usage()
        
        # 根据CPU和内存使用率调整工作线程数
        if cpu_usage > 70 or memory_usage > 80:
            # 高负载，减少线程数
            self.current_workers = max(
                self.min_workers,
                int(self.current_workers * 0.8)
            )
        elif cpu_usage < 30:
            # 低负载，增加线程数
            self.current_workers = min(
                self.max_workers,
                int(self.current_workers * 1.2)
            )
        
        return self.current_workers
    
    async def process_with_adaptive_batch_async(self, items: List[T]) -> List[R]:
        """带自适应批处理大小的异步处理

        Args:
            items: 要处理的项目列表

        Returns:
            List[R]: 处理结果列表
        """
        if not items:
            return []

        results = []

        # 重置中断标志
        self.reset_interrupted()

        i = 0
        while i < len(items):
            # 检查中断标志
            if self.check_interrupted():
                break

            # 调整批处理大小
            batch_size = self.adjust_batch_size()

            # 获取当前批次
            batch_end = min(i + batch_size, len(items))
            batch = items[i:batch_end]

            # 使用asyncio.to_thread处理当前批次
            tasks = [asyncio.to_thread(self._processor_func, item) for item in batch]
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)

            # 过滤异常结果
            valid_results = [r for r in batch_results if not isinstance(r, Exception)]
            results.extend(valid_results)
            i = batch_end

            # 让出控制权，让系统资源监控更新
            await asyncio.sleep(0.01)

        return results

    def process_with_adaptive_batch(self, items: List[T]) -> List[R]:
        """带自适应批处理大小的处理（同步兼容接口）

        Args:
            items: 要处理的项目列表

        Returns:
            List[R]: 处理结果列表
        """
        if not items:
            return []

        # 尝试使用异步方法
        try:
            from src.utils.async_manager import get_async_manager
            async_manager = get_async_manager()

            if async_manager.is_available():
                future = asyncio.run_coroutine_threadsafe(
                    self.process_with_adaptive_batch_async(items),
                    async_manager.get_event_loop()
                )
                return future.result()
            else:
                # 回退到原有实现
                return self._process_with_adaptive_batch_sync(items)

        except Exception as e:
            logger.error(f"自适应异步批处理失败，使用同步回退方案: {e}")
            return self._process_with_adaptive_batch_sync(items)

    def _process_with_adaptive_batch_sync(self, items: List[T]) -> List[R]:
        """自适应批处理同步实现（回退方案）"""
        results = []

        # 重置中断标志
        self.reset_interrupted()

        i = 0
        while i < len(items):
            # 检查中断标志
            if self.check_interrupted():
                break

            # 调整批处理大小和工作线程数
            batch_size = self.adjust_batch_size()
            workers = self.adjust_workers()

            # 获取当前批次
            batch_end = min(i + batch_size, len(items))
            batch = items[i:batch_end]

            # 使用调整后的线程数处理当前批次
            with ThreadPoolExecutor(max_workers=workers) as executor:
                batch_results = list(executor.map(self._processor_func, batch))

            results.extend(batch_results)
            i = batch_end

            # 短暂暂停，让系统资源监控更新
            time.sleep(0.01)

        return results