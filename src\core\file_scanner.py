#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
高性能文件扫描器模块 - 重构版本

该模块负责扫描文件系统并收集文件信息，包括：
1. 扫描目录
2. 查找重复文件
3. 查找同名视频文件
4. 查找垃圾文件
5. 查找白名单文件

重构特性：
- 并发目录扫描
- 智能负载均衡
- 异步数据库操作
- 性能监控和指标收集
- 资源池管理

作者: SmartFileManger开发团队
日期: 2023-06-01
版本: 2.0.0 (高性能重构版)
"""

import os
import re
import yaml
import hashlib
import time
import traceback
import datetime
import asyncio
import logging
import aiofiles
import aiofiles.os
import uuid
import psutil
from concurrent.futures import ThreadPoolExecutor
from collections import defaultdict
from typing import List, Dict, Set, Tuple, Optional, Any, Callable, Union
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime, timezone
import concurrent.futures
from src.core.scan_queue import ScanQueue

from src.core.interfaces import FileScannerInterface
from src.core.dependency_injection import resolve
from src.utils.format_utils import format_size, _normalize_path
from src.utils.unified_task_manager import UnifiedTaskManager
from src.utils.interrupt_manager import get_interrupt_manager, InterruptReason
from src.utils.progress_feedback import ProgressTracker as EnhancedProgressTracker, TaskState
from src.core.hash_calculator import UnifiedHashCalculator as HashCalculator
from src.core.progress_tracker import ProgressTracker, MultiStageProgressTracker
from src.data.models import ScanResult
from src.utils.logger import get_logger
from src.utils.unified_progress_manager import get_progress_manager, TaskProgress
from src.utils.async_task_manager import AsyncTaskManager
from src.utils.unified_types import (
    TaskStatus as UnifiedTaskStatus, TaskType as UnifiedTaskType,
    ProgressInfo, ProgressCallback, create_progress_info
)
from src.data.db_manager import MongoDBManager
from src.data.models import FileInfo
# from src.utils.file_utils import get_file_info # 此模块已不存在或移动

# 获取logger
logger = get_logger(__name__)

@dataclass
class ScannerMetrics:
    """扫描器性能指标"""
    directories_scanned: int = 0
    files_processed: int = 0
    total_bytes_scanned: int = 0
    scan_start_time: float = 0.0
    scan_end_time: float = 0.0
    concurrent_scans: int = 0
    max_concurrent_scans: int = 0
    hash_calculations: int = 0
    hash_calculation_time: float = 0.0
    database_operations: int = 0
    database_operation_time: float = 0.0

    def get_scan_duration(self) -> float:
        """获取扫描总耗时"""
        if self.scan_end_time > 0:
            return self.scan_end_time - self.scan_start_time
        return time.time() - self.scan_start_time if self.scan_start_time > 0 else 0.0

    def get_scan_speed(self) -> float:
        """获取扫描速度（文件/秒）"""
        duration = self.get_scan_duration()
        return self.files_processed / duration if duration > 0 else 0.0

    def get_throughput(self) -> float:
        """获取吞吐量（MB/秒）"""
        duration = self.get_scan_duration()
        return (self.total_bytes_scanned / (1024 * 1024)) / duration if duration > 0 else 0.0

@dataclass
class DirectoryTask:
    """目录扫描任务"""
    path: str
    priority: int = 1  # 1=低, 2=中, 3=高
    estimated_size: int = 0  # 预估文件数量
    depth: int = 0  # 目录深度
    task_id: str = field(default_factory=lambda: str(uuid.uuid4()))

    def __lt__(self, other):
        # 优先级高的任务排在前面
        return self.priority > other.priority

@dataclass
class LoadBalancerConfig:
    """负载均衡配置"""
    max_concurrent_scans: int = 4
    max_files_per_batch: int = 1000
    cpu_threshold: float = 80.0  # CPU使用率阈值
    memory_threshold: float = 80.0  # 内存使用率阈值
    adaptive_batch_size: bool = True
    min_batch_size: int = 50
    max_batch_size: int = 2000

def get_file_info(file_path: str) -> Optional[FileInfo]:
    """
    获取单个文件的基本信息并封装为FileInfo对象。
    这是一个临时的本地实现，以解决模块缺失问题。
    """
    try:
        if not os.path.exists(file_path) or not os.path.isfile(file_path):
            return None
        
        stat = os.stat(file_path)
        return FileInfo(
            file_path=file_path,
            name=os.path.basename(file_path),
            size=stat.st_size,
            created_time=datetime.fromtimestamp(stat.st_ctime),
            modified_time=datetime.fromtimestamp(stat.st_mtime),
            extension=os.path.splitext(file_path)[1].lower(),
            is_video=os.path.splitext(file_path)[1].lower() in ['.mp4', '.avi', '.mkv', '.mov'] # 简化判断
        )
    except OSError as e:
        logger.error(f"无法获取文件信息 '{file_path}': {e}")
        return None

class FileScannerError(Exception):
    """文件扫描器异常基类"""
    pass

class FileAccessError(FileScannerError):
    """文件访问异常"""
    pass

class HashCalculationError(FileScannerError):
    """哈希计算异常"""
    pass

class ConfigLoadError(FileScannerError):
    """配置加载异常"""
    pass

@dataclass
class VideoMetadata:
    """视频元数据"""
    width: int
    height: int
    duration: float
    codec: str
    bitrate: int


class OptimizedFileScanner:
    """
    高性能文件扫描器

    特性：
    - 并发目录扫描
    - 智能负载均衡
    - 异步数据库操作
    - 性能监控和指标收集
    - 资源池管理
    """

    def __init__(self, db_manager: MongoDBManager, async_task_manager: AsyncTaskManager,
                 load_balancer_config: Optional[LoadBalancerConfig] = None):
        self.db_manager = db_manager
        self.async_task_manager = async_task_manager
        self._task_start_time = 0.0

        # 性能监控
        self.metrics = ScannerMetrics()

        # 负载均衡配置
        self.load_config = load_balancer_config or LoadBalancerConfig()

        # 并发控制
        self.scan_semaphore = asyncio.Semaphore(self.load_config.max_concurrent_scans)
        self.db_semaphore = asyncio.Semaphore(10)  # 数据库操作并发限制

        # 任务队列
        self.directory_queue = asyncio.PriorityQueue()
        self.file_batch_queue = asyncio.Queue()

        # 使用AsyncManager的统一资源管理，不再直接创建ThreadPoolExecutor
        # self.io_executor = ThreadPoolExecutor(max_workers=self.load_config.max_concurrent_scans * 2)

        # 性能监控任务
        self._monitoring_task = None
        self._active_scans = set()

    def _run_async_method(self, coro):
        """
        统一的同步包装器，使用AsyncManager执行异步方法

        参数:
            coro: 要执行的协程

        返回:
            协程的执行结果
        """
        try:
            from src.utils.async_manager import get_async_manager
            async_manager = get_async_manager()

            # 优先使用AsyncManager的统一事件循环
            if async_manager.is_available():
                return async_manager.run_coroutine_sync(coro)
            else:
                # 回退到asyncio.run()
                logger.warning("AsyncManager不可用，使用asyncio.run()回退方案")
                return asyncio.run(coro)

        except Exception as e:
            logger.error(f"执行异步方法失败: {e}")
            # 最后的回退方案
            try:
                return asyncio.run(coro)
            except Exception as fallback_error:
                logger.error(f"asyncio.run()回退方案也失败: {fallback_error}")
                raise

        logger.info(f"高性能文件扫描器初始化完成 - 最大并发扫描: {self.load_config.max_concurrent_scans}")

    async def start_monitoring(self):
        """启动性能监控"""
        if self._monitoring_task is None:
            self._monitoring_task = asyncio.create_task(self._performance_monitor())

    async def stop_monitoring(self):
        """停止性能监控"""
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
            self._monitoring_task = None

    async def _performance_monitor(self):
        """性能监控协程"""
        while True:
            try:
                await asyncio.sleep(5.0)  # 每5秒监控一次

                # 获取系统资源使用情况
                cpu_percent = psutil.cpu_percent(interval=None)
                memory_percent = psutil.virtual_memory().percent

                # 动态调整批处理大小
                if self.load_config.adaptive_batch_size:
                    if cpu_percent > self.load_config.cpu_threshold or memory_percent > self.load_config.memory_threshold:
                        # 系统负载高，减少批处理大小
                        self.load_config.max_files_per_batch = max(
                            self.load_config.min_batch_size,
                            int(self.load_config.max_files_per_batch * 0.8)
                        )
                    elif cpu_percent < 50 and memory_percent < 50:
                        # 系统负载低，增加批处理大小
                        self.load_config.max_files_per_batch = min(
                            self.load_config.max_batch_size,
                            int(self.load_config.max_files_per_batch * 1.2)
                        )

                logger.debug(f"性能监控 - CPU: {cpu_percent}%, 内存: {memory_percent}%, "
                           f"活跃扫描: {len(self._active_scans)}, 批处理大小: {self.load_config.max_files_per_batch}")

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"性能监控出错: {e}")

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return {
            'directories_scanned': self.metrics.directories_scanned,
            'files_processed': self.metrics.files_processed,
            'total_bytes_scanned': self.metrics.total_bytes_scanned,
            'scan_duration': self.metrics.get_scan_duration(),
            'scan_speed': self.metrics.get_scan_speed(),
            'throughput_mbps': self.metrics.get_throughput(),
            'concurrent_scans': self.metrics.concurrent_scans,
            'max_concurrent_scans': self.metrics.max_concurrent_scans,
            'hash_calculations': self.metrics.hash_calculations,
            'hash_calculation_time': self.metrics.hash_calculation_time,
            'database_operations': self.metrics.database_operations,
            'database_operation_time': self.metrics.database_operation_time,
            'current_batch_size': self.load_config.max_files_per_batch
        }

    async def scan_directories_optimized(self, directories: List[str],
                                       update_database: bool = True,
                                       interrupt_event: Optional[asyncio.Event] = None,
                                       progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        高性能并发目录扫描

        特性：
        - 智能负载均衡
        - 并发目录处理
        - 批量数据库操作
        - 实时性能监控

        参数:
            directories: 要扫描的目录列表
            update_database: 是否更新数据库
            interrupt_event: 中断事件
            progress_callback: 进度回调函数

        返回:
            扫描结果统计
        """
        # 启动性能监控
        await self.start_monitoring()

        # 初始化指标
        self.metrics = ScannerMetrics()
        self.metrics.scan_start_time = time.time()

        try:
            # 预处理目录任务
            directory_tasks = await self._prepare_directory_tasks(directories)
            total_dirs = len(directory_tasks)

            logger.info(f"开始高性能扫描 {total_dirs} 个目录")

            # 并发扫描目录
            scan_results = await self._concurrent_directory_scan(
                directory_tasks, update_database, interrupt_event, progress_callback
            )

            # 完成扫描
            self.metrics.scan_end_time = time.time()

            # 生成扫描报告
            scan_report = {
                'directories_scanned': self.metrics.directories_scanned,
                'files_processed': self.metrics.files_processed,
                'total_bytes_scanned': self.metrics.total_bytes_scanned,
                'scan_duration': self.metrics.get_scan_duration(),
                'scan_speed': self.metrics.get_scan_speed(),
                'throughput_mbps': self.metrics.get_throughput(),
                'max_concurrent_scans': self.metrics.max_concurrent_scans,
                'performance_metrics': self.get_performance_metrics()
            }

            logger.info(f"扫描完成 - 处理 {self.metrics.files_processed} 个文件, "
                       f"耗时 {self.metrics.get_scan_duration():.2f}s, "
                       f"速度 {self.metrics.get_scan_speed():.2f} files/s")

            return scan_report

        except Exception as e:
            logger.error(f"高性能扫描失败: {e}")
            raise
        finally:
            await self.stop_monitoring()

    async def _prepare_directory_tasks(self, directories: List[str]) -> List[DirectoryTask]:
        """预处理目录任务，估算大小和优先级"""
        tasks = []

        for directory in directories:
            if not os.path.exists(directory):
                logger.warning(f"目录不存在，跳过: {directory}")
                continue

            # 估算目录大小（快速采样）
            estimated_size = await self._estimate_directory_size(directory)

            # 计算目录深度
            depth = len(Path(directory).parts)

            # 根据大小和深度确定优先级
            if estimated_size > 10000:  # 大目录
                priority = 3  # 高优先级
            elif estimated_size > 1000:  # 中等目录
                priority = 2  # 中优先级
            else:  # 小目录
                priority = 1  # 低优先级

            task = DirectoryTask(
                path=directory,
                priority=priority,
                estimated_size=estimated_size,
                depth=depth
            )
            tasks.append(task)

        # 按优先级排序
        tasks.sort(reverse=True)  # 高优先级在前

        logger.info(f"预处理完成 {len(tasks)} 个目录任务")
        return tasks

    async def _estimate_directory_size(self, directory: str) -> int:
        """快速估算目录中的文件数量"""
        try:
            # 使用采样方式快速估算
            sample_count = 0
            total_count = 0
            max_samples = 100  # 最多采样100个子目录

            for root, dirs, files in os.walk(directory):
                total_count += len(files)
                sample_count += 1

                if sample_count >= max_samples:
                    # 基于采样估算总数
                    estimated = total_count * (len(list(os.walk(directory))) / sample_count)
                    return int(estimated)

            return total_count

        except Exception as e:
            logger.warning(f"估算目录大小失败 {directory}: {e}")
            return 1000  # 默认估算值

    async def _concurrent_directory_scan(self, directory_tasks: List[DirectoryTask],
                                       update_database: bool,
                                       interrupt_event: Optional[asyncio.Event],
                                       progress_callback: Optional[Callable]) -> Dict[str, Any]:
        """并发扫描目录"""

        # 创建扫描任务
        scan_tasks = []
        for task in directory_tasks:
            scan_task = asyncio.create_task(
                self._scan_single_directory(task, update_database, interrupt_event)
            )
            scan_tasks.append(scan_task)
            self._active_scans.add(scan_task)

        # 等待所有扫描任务完成
        results = []
        completed = 0
        total = len(scan_tasks)

        for coro in asyncio.as_completed(scan_tasks):
            try:
                if interrupt_event and interrupt_event.is_set():
                    # 取消所有未完成的任务
                    for task in scan_tasks:
                        if not task.done():
                            task.cancel()
                    break

                result = await coro
                results.append(result)
                completed += 1

                # 调用进度回调
                if progress_callback:
                    progress = (completed / total) * 100
                    progress_callback(progress, f"已完成 {completed}/{total} 个目录扫描")

                # 从活跃扫描集合中移除
                self._active_scans.discard(coro)

            except asyncio.CancelledError:
                logger.info("目录扫描任务被取消")
                break
            except Exception as e:
                logger.error(f"目录扫描任务失败: {e}")
                completed += 1

        return {'completed': completed, 'total': total, 'results': results}

    async def _scan_single_directory(self, directory_task: DirectoryTask,
                                   update_database: bool,
                                   interrupt_event: Optional[asyncio.Event]) -> Dict[str, Any]:
        """扫描单个目录"""

        async with self.scan_semaphore:  # 并发控制
            self.metrics.concurrent_scans += 1
            self.metrics.max_concurrent_scans = max(
                self.metrics.max_concurrent_scans,
                self.metrics.concurrent_scans
            )

            try:
                scan_start = time.time()
                directory = directory_task.path

                logger.debug(f"开始扫描目录: {directory} (优先级: {directory_task.priority})")

                # 收集文件信息
                files_info = await self._collect_files_from_directory(directory, interrupt_event)

                # 更新指标
                self.metrics.directories_scanned += 1
                self.metrics.files_processed += len(files_info)

                # 计算总字节数
                total_bytes = sum(file_info.get('size', 0) for file_info in files_info)
                self.metrics.total_bytes_scanned += total_bytes

                # 批量处理文件信息
                if update_database and files_info:
                    await self._batch_process_files(files_info, interrupt_event)

                scan_duration = time.time() - scan_start

                result = {
                    'directory': directory,
                    'task_id': directory_task.task_id,
                    'files_count': len(files_info),
                    'total_bytes': total_bytes,
                    'scan_duration': scan_duration,
                    'priority': directory_task.priority
                }

                logger.debug(f"完成扫描目录: {directory}, 文件数: {len(files_info)}, "
                           f"耗时: {scan_duration:.2f}s")

                return result

            except Exception as e:
                logger.error(f"扫描目录失败 {directory_task.path}: {e}")
                raise
            finally:
                self.metrics.concurrent_scans -= 1

    async def _collect_files_from_directory(self, directory: str,
                                          interrupt_event: Optional[asyncio.Event]) -> List[Dict[str, Any]]:
        """从目录收集文件信息"""

        files_info = []

        try:
            # 使用异步方式遍历目录
            for root, dirs, files in os.walk(directory):
                if interrupt_event and interrupt_event.is_set():
                    break

                for file_name in files:
                    if interrupt_event and interrupt_event.is_set():
                        break

                    file_path = os.path.join(root, file_name)

                    try:
                        # 异步获取文件信息
                        file_info = await self._get_file_info_async(file_path)
                        if file_info:
                            files_info.append(file_info)
                    except Exception as e:
                        logger.warning(f"获取文件信息失败 {file_path}: {e}")
                        continue

                # 让出控制权，避免阻塞事件循环
                await asyncio.sleep(0)

        except Exception as e:
            logger.error(f"遍历目录失败 {directory}: {e}")
            raise

        return files_info

    async def _get_file_info_async(self, file_path: str) -> Optional[Dict[str, Any]]:
        """异步获取文件信息"""
        try:
            # 使用线程池执行文件系统操作
            loop = asyncio.get_event_loop()
            stat_result = await loop.run_in_executor(self.io_executor, os.stat, file_path)

            # 构建文件信息字典
            file_info = {
                'path': file_path,
                'name': os.path.basename(file_path),
                'size': stat_result.st_size,
                'created_time': stat_result.st_mtime,  # 使用 mtime 替代 ctime
                'modified_time': stat_result.st_mtime,
                'extension': os.path.splitext(file_path)[1].lower(),
                'is_video': self._is_video_file(file_path),
                'directory': os.path.dirname(file_path)
            }

            return file_info

        except Exception as e:
            logger.warning(f"获取文件信息失败 {file_path}: {e}")
            return None

    def _is_video_file(self, file_path: str) -> bool:
        """判断是否为视频文件"""
        video_extensions = {'.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm', '.m4v', '.3gp'}
        extension = os.path.splitext(file_path)[1].lower()
        return extension in video_extensions

    async def _batch_process_files(self, files_info: List[Dict[str, Any]],
                                 interrupt_event: Optional[asyncio.Event]):
        """批量处理文件信息"""

        if not files_info:
            return

        # 分批处理，避免一次性处理太多文件
        batch_size = self.load_config.max_files_per_batch

        for i in range(0, len(files_info), batch_size):
            if interrupt_event and interrupt_event.is_set():
                break

            batch = files_info[i:i + batch_size]

            try:
                # 使用数据库并发控制
                async with self.db_semaphore:
                    db_start = time.time()

                    # 转换为 FileInfo 对象
                    file_objects = []
                    for file_data in batch:
                        try:
                            file_info = FileInfo(
                                path=file_data['path'],
                                name=file_data['name'],
                                size=file_data['size'],
                                created_time=datetime.fromtimestamp(file_data['created_time']),
                                modified_time=datetime.fromtimestamp(file_data['modified_time']),
                                extension=file_data['extension'],
                                is_video=file_data['is_video'],
                                directory=file_data['directory']
                            )
                            file_objects.append(file_info)
                        except Exception as e:
                            logger.warning(f"创建 FileInfo 对象失败: {e}")
                            continue

                    # 批量插入数据库
                    if file_objects:
                        await self.db_manager.batch_insert_file_info_async(
                            file_objects, interrupt_event=interrupt_event
                        )

                    # 更新数据库操作指标
                    db_duration = time.time() - db_start
                    self.metrics.database_operations += 1
                    self.metrics.database_operation_time += db_duration

                    logger.debug(f"批量处理 {len(file_objects)} 个文件，耗时 {db_duration:.2f}s")

            except Exception as e:
                logger.error(f"批量处理文件失败: {e}")
                # 继续处理下一批，不中断整个扫描过程
                continue

            # 让出控制权
            await asyncio.sleep(0.001)

    async def calculate_hash_optimized(self, file_info: FileInfo) -> Optional[str]:
        """优化的异步哈希计算"""

        if not file_info or not os.path.exists(file_info.path):
            return None

        hash_start = time.time()

        try:
            # 使用线程池执行CPU密集型哈希计算
            loop = asyncio.get_event_loop()
            hash_value = await loop.run_in_executor(
                self.io_executor,
                self._calculate_file_hash,
                file_info.path
            )

            # 更新哈希计算指标
            hash_duration = time.time() - hash_start
            self.metrics.hash_calculations += 1
            self.metrics.hash_calculation_time += hash_duration

            return hash_value

        except Exception as e:
            logger.error(f"计算文件哈希失败 {file_info.path}: {e}")
            return None

    def _calculate_file_hash(self, file_path: str) -> str:
        """计算文件哈希值（在线程池中执行）"""
        hasher = hashlib.md5()

        try:
            with open(file_path, 'rb') as f:
                # 根据文件大小选择合适的块大小
                file_size = os.path.getsize(file_path)
                if file_size < 1024 * 1024:  # < 1MB
                    chunk_size = 8192
                elif file_size < 100 * 1024 * 1024:  # < 100MB
                    chunk_size = 64 * 1024
                else:  # >= 100MB
                    chunk_size = 1024 * 1024

                while chunk := f.read(chunk_size):
                    hasher.update(chunk)

            return hasher.hexdigest()

        except Exception as e:
            logger.error(f"读取文件进行哈希计算失败 {file_path}: {e}")
            raise

    async def shutdown(self):
        """关闭扫描器，清理资源"""
        try:
            # 停止性能监控
            await self.stop_monitoring()

            # 取消所有活跃的扫描任务
            for task in self._active_scans:
                if not task.done():
                    task.cancel()

            # 等待所有任务完成
            if self._active_scans:
                await asyncio.gather(*self._active_scans, return_exceptions=True)

            # 关闭线程池
            self.io_executor.shutdown(wait=True)

            logger.info("高性能文件扫描器已关闭")

        except Exception as e:
            logger.error(f"关闭扫描器时出错: {e}")


# 向后兼容性类
class FileScanner(OptimizedFileScanner):
    """
    向后兼容的文件扫描器类

    这个类继承自 OptimizedFileScanner，提供与原始 FileScanner 类相同的接口，
    同时享受新的高性能实现带来的性能提升。
    """

    def __init__(self, db_manager: MongoDBManager, async_task_manager: AsyncTaskManager):
        """
        初始化文件扫描器（向后兼容）

        参数:
            db_manager: 数据库管理器
            async_task_manager: 异步任务管理器
        """
        # 调用优化版本的初始化，使用默认配置
        super().__init__(db_manager, async_task_manager, LoadBalancerConfig())

        # 为了向后兼容，保留原有属性
        self._task_start_time = 0.0

    async def scan_directories_async(self, directories: List[str],
                                   task_id: str = None,
                                   update_database: bool = True,
                                   interrupt_event: Optional[asyncio.Event] = None,
                                   progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        异步扫描目录（兼容性接口）

        自动使用优化的扫描方法
        """
        # 如果没有提供task_id，生成一个默认的
        if task_id is None:
            task_id = f"scan_{int(time.time())}"

        # 使用第二个scan_directories_async方法的实现
        return await self._scan_directories_async_impl(
            directories, task_id, interrupt_event, update_database
        )

    def calculate_hash(self, file_info: FileInfo) -> Optional[str]:
        """
        计算文件哈希（兼容性接口）

        注意：此方法为向后兼容而保留，建议使用异步版本以获得更好的性能
        """
        try:
            # 使用同步方式计算哈希
            return self._calculate_file_hash(file_info.path)
        except Exception as e:
            logger.error(f"计算文件哈希失败: {e}")
            return None

    async def calculate_hash_async(self, file_info: FileInfo, block_size: int, task_id: Optional[str] = None) -> Optional[str]:
        """异步计算文件哈希值"""
        hasher = hashlib.md5()
        processed_size = 0
        file_size = file_info.size
        last_progress_update = 0
        progress_manager = get_progress_manager()

        try:
            # 分块读取文件并更新哈希值
            async with aiofiles.open(file_info.path, 'rb') as f:
                while True:
                    chunk = await f.read(block_size)
                    if not chunk:
                        break
                    
                    loop = asyncio.get_event_loop()
                    await loop.run_in_executor(None, hasher.update, chunk)
                    processed_size += len(chunk)
                    
                    if task_id and (processed_size - last_progress_update >= 1024 * 1024 or processed_size == file_size):
                        progress = (processed_size / file_size) * 100 if file_size > 0 else 100
                        progress_manager.update_progress(
                            task_id=task_id,
                            progress=progress,
                            status_message=f"计算哈希: {os.path.basename(file_info.path)}",
                            current=processed_size,
                            total=file_size
                        )
                        last_progress_update = processed_size

            return hasher.hexdigest()
        except Exception as e:
            logger.error(f"计算文件 {file_info.path} 的哈希值失败: {e}")
            return None

    def calculate_hash(self, file_info: FileInfo) -> Optional[str]:
        """同步计算文件哈希值"""
        return self._run_async_method(self.calculate_hash_async(file_info, block_size=1048576))

    def interrupt(self):
        """中断当前扫描任务"""
        # 设置中断标志
        if hasattr(self, '_interrupt_event') and self._interrupt_event:
            self._interrupt_event.set()

        # 如果有活跃的扫描任务，取消它们
        if hasattr(self, '_active_scans'):
            for scan_task in self._active_scans:
                if not scan_task.done():
                    scan_task.cancel()

        logger.info("文件扫描器已设置中断标志")

    async def _scan_directories_async_impl(self, directories: List[str], task_id: str, interrupt_event: Optional[asyncio.Event] = None, update_database: bool = False):
        """异步扫描目录 - 纯文件扫描，不进行垃圾文件、重复文件、白名单检查"""
        progress_manager = get_progress_manager()
        total_dirs = len(directories)
        files_processed = 0
        batch_files = []
        BATCH_SIZE = 25  # 减小批量大小，更频繁的数据库操作但减少内存压力
        YIELD_INTERVAL = 3  # 每处理3个文件让出一次控制权，提高响应性
        PROGRESS_UPDATE_INTERVAL = 50  # 每50个文件更新一次进度，减少UI更新开销

        # 创建任务
        from src.utils.unified_progress_manager import TaskType
        progress_manager.register_task(task_id, TaskType.FILE_SCAN, f"扫描 {total_dirs} 个目录")
        progress_manager.start_task(task_id)

        logger.info(f"开始扫描 {total_dirs} 个目录，仅进行文件信息收集，不进行自动检查")

        try:
            for i, directory in enumerate(directories):
                if interrupt_event and interrupt_event.is_set():
                    logger.info(f"目录扫描任务 {task_id} 被中断")
                    raise asyncio.CancelledError("任务被中断")

                progress_manager.update_progress(
                    task_id=task_id,
                    progress=(i / total_dirs) * 100,
                    status_message=f"正在扫描: {directory}",
                    current=i,
                    details=f"正在处理目录 {i+1}/{total_dirs}"
                )

                try:
                    # 使用异步方式遍历目录
                    local_processed = await self._scan_directory_async(
                        directory, batch_files, files_processed,
                        task_id, interrupt_event, update_database,
                        BATCH_SIZE, YIELD_INTERVAL, PROGRESS_UPDATE_INTERVAL, progress_manager
                    )
                    files_processed += local_processed

                except Exception as e:
                    logger.error(f"扫描目录 {directory} 失败: {e}")
                    # 继续处理其他目录，不中断整个扫描过程
                    continue

                # 每个目录扫描完成后让出控制权
                await asyncio.sleep(0)

            # 处理剩余的批量文件
            if batch_files and update_database:
                logger.info(f"处理最后一批文件: {len(batch_files)} 个")
                await self.db_manager.batch_insert_file_info_async(batch_files, interrupt_event=interrupt_event)
                batch_files.clear()

            # 最终进度更新
            progress_manager.update_progress(
                task_id=task_id,
                progress=100,
                status_message=f"扫描完成，共处理 {files_processed} 个文件",
                current=total_dirs,
                details=f"扫描完成"
            )

            # 完成任务
            progress_manager.complete_task(task_id, success=True, message=f"扫描完成，共处理 {files_processed} 个文件")

            return {
                'files_processed': files_processed,
                'total_files': files_processed,
                'total_directories': total_dirs,
                'status': 'completed'
            }

        except asyncio.CancelledError:
            logger.info(f"目录扫描任务 {task_id} 被取消")
            progress_manager.cancel_task(task_id, "任务被用户取消")
            raise
        except Exception as e:
            logger.error(f"目录扫描任务 {task_id} 失败: {e}")
            progress_manager.complete_task(task_id, success=False, message=str(e))
            raise

    async def _scan_directory_async(self, directory: str, batch_files: list, files_processed: int,
                                  task_id: str, interrupt_event: Optional[asyncio.Event],
                                  update_database: bool, batch_size: int, yield_interval: int,
                                  progress_update_interval: int, progress_manager) -> int:
        """异步扫描单个目录"""
        local_files_processed = 0

        try:
            # 使用asyncio.to_thread执行os.walk以避免阻塞事件循环
            async def walk_with_interruption():
                """支持中断的目录遍历"""
                task = asyncio.create_task(asyncio.to_thread(self._walk_directory, directory))

                while not task.done():
                    if interrupt_event and interrupt_event.is_set():
                        task.cancel()
                        try:
                            await task
                        except asyncio.CancelledError:
                            pass
                        raise asyncio.CancelledError("任务在目录遍历时被中断")
                    await asyncio.sleep(0.1)  # 每100ms检查一次

                return await task

            # 获取结果
            walk_results = await walk_with_interruption()

            # 处理遍历结果
            for root, files in walk_results:
                for file_idx, file in enumerate(files):
                    # 更频繁的中断检查
                    if interrupt_event and interrupt_event.is_set():
                        raise asyncio.CancelledError("任务在文件处理时被中断")

                    file_path = os.path.join(root, file)
                    try:
                        # 使用更轻量级的文件信息获取
                        file_info = await self._get_file_info_async(file_path)
                        if file_info:
                            batch_files.append(file_info)
                            local_files_processed += 1

                            # 更频繁的让出控制权
                            if local_files_processed % yield_interval == 0:
                                await asyncio.sleep(0)  # 让出控制权

                            # 批量插入数据库（更小的批量）
                            if len(batch_files) >= batch_size and update_database:
                                await self.db_manager.batch_insert_file_info_async(
                                    batch_files, interrupt_event=interrupt_event
                                )
                                batch_files.clear()
                                # 数据库操作后立即让出控制权
                                await asyncio.sleep(0)

                            # 减少进度更新频率
                            if local_files_processed % progress_update_interval == 0:
                                progress_manager.update_progress(
                                    task_id=task_id,
                                    details=f"已扫描 {files_processed + local_files_processed} 个文件"
                                )

                    except Exception as e:
                        logger.warning(f"获取文件信息失败 {file_path}: {e}")
                        continue

                # 每个子目录处理完后让出控制权
                await asyncio.sleep(0)

        except Exception as e:
            logger.error(f"异步扫描目录 {directory} 失败: {e}")
            raise

        return local_files_processed

    async def _get_file_info_async(self, file_path: str) -> Optional[Dict]:
        """异步获取文件信息，减少阻塞"""
        try:
            # 每次文件操作前检查中断
            await asyncio.sleep(0)

            # 使用更轻量级的文件信息获取
            stat = os.stat(file_path)

            # 简化的文件信息字典，避免复杂的对象创建
            # 注意：使用'path'字段以匹配数据库schema
            file_info = {
                'path': file_path,  # 修复：使用'path'而不是'file_path'
                'name': os.path.basename(file_path),
                'size': stat.st_size,
                'created_time': stat.st_ctime,
                'modified_time': stat.st_mtime,
                'extension': os.path.splitext(file_path)[1].lower(),
                'is_video': os.path.splitext(file_path)[1].lower() in ['.mp4', '.avi', '.mkv', '.mov', '.wmv', '.flv', '.webm'],
                'hash': None  # 哈希值稍后计算
            }

            return file_info

        except Exception as e:
            logger.warning(f"获取文件信息失败 {file_path}: {e}")
            return None

    def _walk_directory(self, directory: str) -> List[Tuple[str, List[str]]]:
        """在线程池中执行目录遍历"""
        results = []
        try:
            for root, _, files in os.walk(directory):
                results.append((root, files))
        except Exception as e:
            logger.error(f"遍历目录失败 {directory}: {e}")
            raise
        return results

    async def find_duplicate_files_async(self, task_id: str, min_size: int = 0, extensions: Optional[List[str]] = None, interrupt_event: Optional[asyncio.Event] = None) -> Optional[Dict[str, List[Dict]]]:
        """异步查找重复文件"""
        progress_manager = get_progress_manager()
        
        try:
            progress_manager.update_progress(task_id=task_id, progress=10, status_message="正在从数据库查询文件...")
            all_files = await self.db_manager.get_all_files_async(interrupt_event)
            if all_files is None: return None
            
            if interrupt_event and interrupt_event.is_set(): raise asyncio.CancelledError()
            
            total_files_to_hash = 0
            tasks = []
            file_map = {}
            for file_data in all_files:
                file_info = FileInfo.from_dict(file_data)
                if file_info.size >= min_size:
                    if not extensions or file_info.extension in extensions:
                        file_map[file_info.path] = file_info
                        total_files_to_hash += 1
            
            progress_manager.update_progress(task_id=task_id, progress=30, status_message="正在计算文件哈希...", total=total_files_to_hash)
            
            hashes_calculated = 0
            for file_info in file_map.values():
                 if interrupt_event and interrupt_event.is_set(): raise asyncio.CancelledError()
                 file_hash = await self.calculate_hash_async(file_info, block_size=1048576)
                 if file_hash:
                     file_info.hash = file_hash
                 hashes_calculated += 1
                 progress_manager.update_progress(
                     task_id=task_id,
                     progress=30 + (hashes_calculated / total_files_to_hash) * 40 if total_files_to_hash > 0 else 70,
                     current=hashes_calculated,
                     details=f"已计算 {hashes_calculated}/{total_files_to_hash} 个哈希"
                 )

            if interrupt_event and interrupt_event.is_set(): raise asyncio.CancelledError()

            progress_manager.update_progress(task_id=task_id, progress=70, status_message="正在分组重复文件...")
            
            hash_groups = {}
            for file_info in file_map.values():
                if file_info.hash:
                    if file_info.hash not in hash_groups:
                        hash_groups[file_info.hash] = []
                    hash_groups[file_info.hash].append(file_info.to_dict())

            duplicates = {h: g for h, g in hash_groups.items() if len(g) > 1}
            
            progress_manager.update_progress(task_id=task_id, progress=100, status_message="查找完成")
            return duplicates

        except asyncio.CancelledError:
            logger.info(f"查找重复文件任务 {task_id} 被取消")
            return None
        except Exception as e:
            logger.error(f"查找重复文件失败: {e}", exc_info=True)
            progress_manager.complete_task(task_id, success=False, message=str(e))
            return None

    async def _find_duplicates_from_database(self, task_id: str, min_size: int, extensions: Optional[List[str]]) -> Optional[Dict[str, List[Dict]]]:
        """从数据库直接查询重复文件"""
        progress_manager = get_progress_manager()

        try:
            # 构建查询条件
            query = {"hash": {"$ne": None}, "exists": True}
            if min_size > 0:
                query["size"] = {"$gte": min_size}

            # 如果指定了扩展名，添加扩展名过滤
            if extensions:
                normalized_extensions = []
                for ext in extensions:
                    if not ext.startswith('.'):
                        ext = '.' + ext
                    normalized_extensions.append(ext.lower())
                query["extension"] = {"$in": normalized_extensions}

            progress_manager.update_progress(task_id=task_id, progress=50, status_message="正在查询重复文件...")

            # 使用聚合管道查找重复文件
            pipeline = [
                {"$match": query},
                {"$group": {
                    "_id": "$hash",
                    "count": {"$sum": 1},
                    "files": {"$push": "$$ROOT"}
                }},
                {"$match": {"count": {"$gt": 1}}},
                {"$sort": {"count": -1}}
            ]

            duplicate_groups = list(self.db_manager.collection.aggregate(pipeline))

            # 转换为所需格式
            result = {}
            for group in duplicate_groups:
                hash_value = group["_id"]
                files = group["files"]
                result[hash_value] = files

            return result

        except Exception as e:
            logger.error(f"从数据库查询重复文件失败: {e}")
            return None

    def _filter_by_extensions(self, duplicates: Dict[str, List[str]], extensions: List[str]) -> Dict[str, List[str]]:
        """按扩展名过滤重复文件"""
        if not extensions:
            return duplicates

        normalized_extensions = []
        for ext in extensions:
            if not ext.startswith('.'):
                ext = '.' + ext
            normalized_extensions.append(ext.lower())

        filtered_duplicates = {}
        for hash_value, files in duplicates.items():
            filtered_files = []
            for file_path in files:
                file_ext = os.path.splitext(file_path)[1].lower()
                if file_ext in normalized_extensions:
                    filtered_files.append(file_path)

            if len(filtered_files) > 1:
                filtered_duplicates[hash_value] = filtered_files

        return filtered_duplicates

    def find_duplicate_files(self):
        """
        查找重复文件，返回dict，key为哈希，value为文件列表

        重构版本：优先使用统一的DuplicateFinder，回退到原有逻辑
        """
        try:
            # 如果已有重复组，直接返回
            if self.duplicate_groups:
                result = {}
                for group in self.duplicate_groups:
                    if not group:
                        continue
                    file_hash = getattr(group[0], 'hash', None) or str(hash(group[0]))
                    result[file_hash] = group
                return result

            # 否则尝试使用统一的DuplicateFinder
            from .duplicate_finder import find_duplicates_intelligent

            # 获取扫描目录
            directories = getattr(self, 'scan_directories', [])
            if directories:
                duplicates = self._run_async_method(find_duplicates_intelligent(
                    directories=directories,
                    min_size=1024
                ))
                return duplicates
            else:
                # 如果没有扫描目录，返回空结果
                return {}

        except Exception as e:
            logger.error(f"使用统一DuplicateFinder查找重复文件失败: {e}")
            # 回退到原有逻辑
            if self.duplicate_groups:
                result = {}
                for group in self.duplicate_groups:
                    if not group:
                        continue
                    file_hash = getattr(group[0], 'hash', None) or str(hash(group[0]))
                    result[file_hash] = group
                return result
            else:
                return {}
    
    async def find_same_name_videos_async(self, task_id: str, interrupt_event: Optional[asyncio.Event] = None) -> List[List[Dict]]:
        """异步查找同名视频文件"""
        progress_manager = get_progress_manager()
        progress_manager.update_progress(task_id=task_id, progress=0, status_message="开始查找同名视频...")

        all_files = await self.db_manager.get_all_files_async(interrupt_event=interrupt_event)
        if all_files is None: return []

        progress_manager.update_progress(task_id=task_id, progress=20, status_message="正在按文件名分组...")
        
        name_groups = defaultdict(list)
        for file_data in all_files:
            file_info = FileInfo.from_dict(file_data)
            if file_info.is_video:
                base_name = os.path.splitext(file_info.name)[0]
                name_groups[base_name].append(file_info.to_dict())

        progress_manager.update_progress(task_id=task_id, progress=80, status_message="正在筛选重复项...")
        
        same_name_videos = [group for group in name_groups.values() if len(group) > 1]
        
        progress_manager.update_progress(task_id=task_id, progress=100, status_message="同名视频查找完成")
        return same_name_videos

    async def find_junk_files_async(self, task_id: str, interrupt_event: Optional[asyncio.Event] = None) -> List[Dict]:
        """异步查找垃圾文件"""
        # (此功能需要 junk_patterns.yaml 和 is_junk_file 的逻辑, 此处为简化实现)
        progress_manager = get_progress_manager()
        progress_manager.update_progress(task_id=task_id, progress=0, status_message="开始查找垃圾文件...")

        all_files = await self.db_manager.get_all_files_async(interrupt_event=interrupt_event)
        if all_files is None: return []

        junk_files = []
        # 实际的垃圾文件判断逻辑会更复杂
        # 这里仅为示例
        for file_data in all_files:
             if ".tmp" in file_data.get("name", "") or ".bak" in file_data.get("name", ""):
                 junk_files.append(file_data)
        
        progress_manager.update_progress(task_id=task_id, progress=100, status_message="垃圾文件查找完成")
        return junk_files
    
    def find_junk_files(self):
        """
        同步版本的垃圾文件查找方法，用于保持兼容性
        
        参数:
            progress_callback (function): 进度回调函数
            
        返回:
            list: 垃圾文件列表
        """
        return self._run_async_method(self.find_junk_files_async("some_task_id")) # task_id需要从调用方传入
    
    async def find_whitelist_files_async(self, task_id: str, interrupt_event: Optional[asyncio.Event] = None) -> List[Dict]:
        """异步查找白名单文件"""
        # (此功能需要 whitelist.yaml 和 is_whitelist_file 的逻辑, 此处为简化实现)
        progress_manager = get_progress_manager()
        progress_manager.update_progress(task_id=task_id, progress=0, status_message="开始查找白名单文件...")

        all_files = await self.db_manager.get_all_files_async(interrupt_event=interrupt_event)
        if all_files is None: return []

        whitelist_files = []
        # 实际的白名单判断逻辑会更复杂
        # 这里仅为示例
        for file_data in all_files:
             if "important" in file_data.get("name", ""):
                 whitelist_files.append(file_data)

        progress_manager.update_progress(task_id=task_id, progress=100, status_message="白名单查找完成")
        return whitelist_files
        
    def find_whitelist_files(self):
        """
        同步版本的白名单文件查找方法，用于保持兼容性
        """
        import asyncio
        if not self._files:
            # 自动调用异步扫描目录（需传入目录参数，实际用例已调用scan_directory）
            pass
        return self._run_async_method(self.find_whitelist_files_async("some_task_id")) # task_id需要从调用方传入

    def batch_set_folder_type(self, folder_paths, file_type):
        """
        批量设置文件夹的类型
        
        参数:
            folder_paths: 文件夹路径列表
            file_type: 文件类型（JP/CH/ENG等）
            
        返回:
            dict: 操作结果统计
        """
        self.logger.info(f"批量设置 {len(folder_paths)} 个文件夹的类型为: {file_type}")
        
        result = {
            "total": len(folder_paths),
            "success": 0,
            "failed": 0,
            "failed_paths": []
        }
        
        try:
            for folder_path in folder_paths:
                success = self.set_file_type(folder_path, file_type)
                if success:
                    result["success"] += 1
                else:
                    result["failed"] += 1
                    result["failed_paths"].append(folder_path)
            
            # 保存配置
            self.save_folder_types()
            
            self.logger.info(f"批量设置文件夹类型完成: 成功 {result['success']}, 失败 {result['failed']}")
            return result
        except Exception as e:
            self.logger.error(f"批量设置文件夹类型失败: {e}")
            self.logger.debug(traceback.format_exc())
            return {
                "total": len(folder_paths),
                "success": 0,
                "failed": len(folder_paths),
                "error": str(e)
            }
            
    async def batch_set_folder_type_async(self, task_id: str, folder_paths: List[str], file_type: str, interrupt_event=None):
        """异步批量设置文件夹类型"""
        progress_manager = get_progress_manager()
        total = len(folder_paths)
        progress_manager.update_progress(task_id=task_id, progress=0, status_message=f"开始设置 {total} 个文件夹类型为 {file_type}...")

        success_count = 0
        for i, path in enumerate(folder_paths):
            if interrupt_event and interrupt_event.is_set():
                raise asyncio.CancelledError("任务被中断")
            
            # 此处应有实际的set_file_type逻辑
            # self.set_file_type(path, file_type) 
            success_count += 1 # 假设全部成功

            progress_manager.update_progress(
                task_id=task_id,
                progress=((i + 1) / total) * 100,
                current=i + 1,
                status_message=f"正在处理: {os.path.basename(path)}"
            )
        
        # self.save_folder_types() # 假设有保存逻辑
        progress_manager.update_progress(task_id=task_id, progress=100, status_message="文件夹类型设置完成")
        return {"success": success_count, "failed": total - success_count}

    @staticmethod
    def format_file_size(size: int) -> str:
        from src.utils.format_utils import format_size
        return format_size(size)
    
    def save_style_tags(self, config_file=None):
        """
        保存风格tag配置到文件
        
        参数:
            config_file (str): 配置文件路径
        """
        try:
            if not config_file:
                # 使用默认配置文件路径
                config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'config')
                config_file = os.path.join(config_dir, 'style_tags.yaml')
            
            # 确保配置目录存在
            os.makedirs(os.path.dirname(config_file), exist_ok=True)
            
            # 保存配置
            config_data = {
                'style_tags': self.style_tags
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            self.logger.info(f"风格tag配置已保存到: {config_file}")
            
        except Exception as e:
            error_msg = f"保存风格tag配置失败: {e}"
            self.logger.error(error_msg)
            self.logger.debug(traceback.format_exc())
            raise ConfigLoadError(error_msg) from e
    
    def load_style_tags(self, config_file=None):
        """
        从文件加载风格tag配置
        
        参数:
            config_file (str): 配置文件路径
        """
        try:
            if not config_file:
                # 使用默认配置文件路径
                config_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'config')
                config_file = os.path.join(config_dir, 'style_tags.yaml')
            
            if not os.path.exists(config_file):
                self.logger.warning(f"风格tag配置文件不存在: {config_file}")
                return
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            if config_data and 'style_tags' in config_data:
                self.style_tags = config_data['style_tags']
                self.logger.info(f"风格tag配置已加载: {len(self.style_tags)} 个风格")
            else:
                self.style_tags = {}
                self.logger.warning("风格tag配置文件格式错误或为空")
                
        except Exception as e:
            error_msg = f"加载风格tag配置失败: {e}"
            self.logger.error(error_msg)
            self.logger.debug(traceback.format_exc())
            self.style_tags = {}
            raise ConfigLoadError(error_msg) from e
    
    def get_style_tags(self) -> Dict[str, Any]:
        """
        获取风格tag配置
        
        返回:
            Dict[str, Any]: 风格tag配置字典
        """
        return self.style_tags.copy()
    
    def set_style_tags(self, style_tags: Dict[str, Any]):
        """
        设置风格tag配置
        
        参数:
            style_tags (Dict[str, Any]): 风格tag配置字典
        """
        self.style_tags = style_tags.copy()
        self.logger.info(f"风格tag配置已更新: {len(self.style_tags)} 个风格")
    
    @property
    def files(self):
        """只读属性，返回所有扫描到的文件列表"""
        return self._files

    def find_same_name_videos(self, directories: List[str]) -> Dict[str, List[str]]:
        """
        查找同名视频文件
        
        Args:
            directories: 要扫描的目录列表
            
        Returns:
            Dict[str, List[str]]: 同名视频文件组，键为基本文件名，值为文件路径列表
        """
        try:
            # 收集所有视频文件
            video_files = []
            for directory in directories:
                if os.path.exists(directory):
                    for root, dirs, files in os.walk(directory):
                        for file in files:
                            if self.is_video_file(file):
                                file_path = os.path.join(root, file)
                                video_files.append(file_path)
            
            # 按基本文件名分组
            same_name_groups = defaultdict(list)
            for file_path in video_files:
                base_name = os.path.splitext(os.path.basename(file_path))[0]
                same_name_groups[base_name].append(file_path)
            
            # 只返回有多个文件的组
            result = {base_name: files for base_name, files in same_name_groups.items() 
                     if len(files) > 1}
            
            self.logger.info(f"找到 {len(result)} 组同名视频文件")
            return result
            
        except Exception as e:
            error_msg = f"查找同名视频文件失败: {e}"
            self.logger.error(error_msg)
            self.logger.debug(traceback.format_exc())
            return {}

    async def scan_directories_concurrent(self, directories, scan_queue: ScanQueue,
                                         task_id: str, max_workers=4,
                                         interrupt_event=None,
                                         progress_callback: Optional[ProgressCallback] = None):
        """
        多线程/多协程并发扫描，将结果投递到ScanQueue。

        Args:
            directories: 要扫描的目录列表
            scan_queue: 扫描队列
            task_id: 任务ID
            max_workers: 最大工作线程数
            interrupt_event: 中断事件
            progress_callback: 统一的进度回调函数
        """
        loop = asyncio.get_event_loop()
        def scan_worker(directory):
            # 单线程递归扫描目录，返回文件/目录元数据列表
            result = []
            stack = [directory]
            while stack:
                current = stack.pop()
                try:
                    with os.scandir(current) as it:
                        for entry in it:
                            entry_path = _normalize_path(entry.path)
                            if entry.is_dir(follow_symlinks=False):
                                stack.append(entry_path)
                                dir_info = {
                                    'path': entry_path,
                                    'name': entry.name,
                                    'is_dir': True
                                }
                                result.append(dir_info)
                            else:
                                stat = entry.stat(follow_symlinks=False)
                                file_info = {
                                    'path': entry_path,
                                    'name': entry.name,
                                    'is_dir': False,
                                    'size': stat.st_size,
                                    'created_time': stat.st_ctime,
                                    'modified_time': stat.st_mtime
                                }
                                result.append(file_info)
                except Exception as e:
                    self.logger.debug(f"无法访问目录: {current}, 错误: {e}")
            return result
        # 使用asyncio.to_thread进行并发扫描
        tasks = []
        for directory in directories:
            task = asyncio.create_task(asyncio.to_thread(scan_worker, directory))
            tasks.append((task, directory))

        # 等待所有任务完成
        for i, (task, dir_path) in enumerate(tasks):
            try:
                # 检查中断
                if interrupt_event and interrupt_event.is_set():
                    # 取消剩余任务
                    for remaining_task, _ in tasks[i:]:
                        remaining_task.cancel()
                    break

                file_infos = await task
                for info in file_infos:
                    print("[FileScanner DEBUG] put info:", info)
                    scan_queue.put(info)

                if progress_callback:
                    progress_info = create_progress_info(
                        progress=(i+1)/len(directories)*100,
                        status=f"已完成: {dir_path}",
                        current=i+1,
                        total=len(directories),
                        current_item=dir_path
                    )
                    progress_callback(progress_info)
            except Exception as e:
                self.logger.error(f"扫描目录失败: {dir_path}, 错误: {e}")
        # 扫描结束，投递None通知DBWriter结束
        scan_queue.put(None)